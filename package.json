{"name": "career-compass-fullstack", "version": "0.1.0", "private": true, "description": "AI-powered career assessment platform with separate frontend and backend", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && bun run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && bun run build", "start": "npm run start:frontend", "start:frontend": "cd frontend && npm run start", "start:backend": "cd backend && bun run start", "lint": "cd frontend && npm run lint", "typecheck": "cd frontend && npm run typecheck && cd ../backend && bun run type-check", "install:all": "npm install && cd frontend && npm install && cd ../backend && bun install", "clean": "rm -rf frontend/node_modules frontend/.next backend/node_modules backend/dist"}, "devDependencies": {"concurrently": "^9.2.0"}, "workspaces": ["frontend", "backend"], "keywords": ["career", "assessment", "ai", "nextjs", "express", "bun", "firebase"], "author": "Career Compass Team", "license": "MIT"}