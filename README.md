# Career Compass - AI-Powered Career Assessment Platform

A comprehensive career assessment platform that provides personalized career recommendations using AI. The platform supports both students and working professionals with tailored questionnaires and insights.

## 🏗️ Architecture

- **Frontend**: Next.js 15 with TypeScript, Tailwind CSS, and Radix UI
- **Backend**: Bun + Express.js with TypeScript
- **AI**: Google Gemini 2.0 Flash via Genkit
- **Database**: Firebase Firestore
- **Authentication**: Firebase Auth (ready for implementation)

## 🚀 Features

### For Students

- Educational path recommendations
- Entry-level career opportunities
- Skill development guidance
- Academic performance analysis
- 5 comprehensive assessment sections with 10-15 questions each

### For Working Professionals

- Career transition guidance
- Leadership development opportunities
- Skill advancement recommendations
- Professional growth strategies
- 5 specialized assessment sections tailored for professionals

### Core Features

- **User Type Selection**: Choose between Student or Professional profiles
- **Dynamic Questionnaires**: Questions loaded from backend based on user type
- **AI-Powered Recommendations**: Personalized career suggestions using Google's Gemini AI
- **Career Insights**: Detailed analysis of recommended career paths
- **Progress Tracking**: Save and resume assessments
- **Responsive Design**: Works seamlessly on desktop and mobile

## 📁 Project Structure

```
career-compass/
├── frontend/                     # Frontend (Next.js)
│   ├── src/
│   │   ├── app/                  # App router pages
│   │   ├── components/           # React components
│   │   ├── context/              # React context providers
│   │   ├── lib/                  # Utilities and types
│   │   └── hooks/                # Custom React hooks
│   ├── package.json              # Frontend dependencies
│   ├── tsconfig.json             # Frontend TypeScript config
│   └── .env.local                # Frontend environment variables
├── backend/                      # Backend (Bun + Express)
│   ├── src/
│   │   ├── config/              # Configuration files
│   │   ├── data/                # Question datasets
│   │   ├── middleware/          # Express middleware
│   │   ├── routes/              # API routes
│   │   ├── services/            # Business logic
│   │   └── types/               # TypeScript types
│   ├── .env.example             # Environment variables template
│   ├── .env                     # Backend environment variables
│   └── FIREBASE_SETUP.md        # Firebase setup guide
├── package.json                 # Root package.json for scripts
└── README.md                    # This file
```

## 🛠️ Setup Instructions

### Prerequisites

- [Node.js](https://nodejs.org/) >= 18
- [Bun](https://bun.sh/) >= 1.0.0
- Firebase project with Firestore and Authentication
- Google AI API key

### 1. Clone the Repository

```bash
git clone <repository-url>
cd career-compass
```

### 2. Install Dependencies

```bash
# Install root dependencies (concurrently for running both servers)
npm install

# Install frontend dependencies
cd frontend
npm install
cd ..

# Install backend dependencies
cd backend
bun install
cd ..
```

### 3. Environment Setup

#### Frontend Environment

Create `.env.local` in the frontend directory:

```bash
cd frontend
echo "NEXT_PUBLIC_API_URL=http://localhost:3001/api" > .env.local
cd ..
```

#### Backend Environment

1. Copy the environment template:

```bash
cd backend
cp .env.example .env
```

2. Follow the [Firebase Setup Guide](backend/FIREBASE_SETUP.md) to get your Firebase credentials

3. Update `backend/.env` with your actual values

### 4. Start Development Servers

```bash
# Start both frontend and backend
npm run dev
```

### 5. Access the Application

- **Frontend**: http://localhost:9002
- **Backend API**: http://localhost:3001
- **Health Check**: http://localhost:3001/api/health

## 📚 API Documentation

### Question Endpoints

- `GET /api/questions/user-types` - Get available user types
- `GET /api/questions/sections?userType=student|professional` - Get question sections
- `POST /api/questions/validate` - Validate assessment answers

### Career Endpoints

- `POST /api/career/assessment` - Process full assessment and get recommendations
- `POST /api/career/insights` - Get insights for specific career path

## 🔧 Development Scripts

```bash
npm run dev                    # Start both frontend and backend
npm run dev:frontend          # Start only frontend
npm run dev:backend           # Start only backend
npm run build                 # Build both frontend and backend
npm run typecheck             # Type checking for both
```

For detailed setup instructions, see the [Firebase Setup Guide](backend/FIREBASE_SETUP.md).
