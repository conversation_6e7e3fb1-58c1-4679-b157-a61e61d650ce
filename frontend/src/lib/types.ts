export enum UserType {
  STUDENT = "student",
  PROFESSIONAL = "professional",
}

export enum QuestionType {
  MCQ = "mcq",
  CHECKBOX = "checkbox",
  SHORT_ANSWER = "short-answer",
  SCALE = "scale",
  YES_NO = "yes-no",
  RANKING = "ranking",
}

export interface Question {
  id: string;
  type: QuestionType;
  text: string;
  options?: string[];
  required?: boolean;
  maxSelections?: number; // For checkbox questions
  scaleMin?: number; // For scale questions
  scaleMax?: number; // For scale questions
  scaleLabels?: string[]; // For scale questions
}

export interface Section {
  id: string;
  title: string;
  description: string;
  questions: Question[];
  userType: UserType;
}

export interface Answers {
  [key: string]: any;
}

export interface AssessmentInput {
  userType: UserType;
  answers: Answers;
}
