/**
 * API client for Career Compass Backend
 */

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001/api";

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface CareerPathRecommendationsInput {
  interestAndSubjectPreferences: string;
  personalityAndLearningStyle: string;
  skillsAndStrengths: string;
  careerAspirationsAndWorkPreferences: string;
  academicBackgroundAndPerformance: string;
}

export interface CareerPathRecommendationsOutput {
  careerRecommendations: string[];
  reasoning: string;
}

export interface CareerInsightsInput {
  careerPath: string;
}

export interface CareerInsightsOutput {
  summary: string;
  futureOutlook: string;
  potentialChallenges: string;
}

export enum UserType {
  STUDENT = "student",
  PROFESSIONAL = "professional",
}

export enum QuestionType {
  MCQ = "mcq",
  CHECKBOX = "checkbox",
  SHORT_ANSWER = "short-answer",
  SCALE = "scale",
  YES_NO = "yes-no",
  RANKING = "ranking",
}

export interface Question {
  id: string;
  type: QuestionType;
  text: string;
  options?: string[];
  required?: boolean;
  maxSelections?: number;
  scaleMin?: number;
  scaleMax?: number;
  scaleLabels?: string[];
}

export interface Section {
  id: string;
  title: string;
  description: string;
  questions: Question[];
  userType: UserType;
}

export interface AssessmentInput {
  userType: UserType;
  answers: Record<string, any>;
}

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;

    const defaultHeaders = {
      "Content-Type": "application/json",
    };

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error("API request failed:", error);
      throw error;
    }
  }

  async getCareerRecommendations(
    input: CareerPathRecommendationsInput
  ): Promise<ApiResponse<CareerPathRecommendationsOutput>> {
    return this.request<CareerPathRecommendationsOutput>(
      "/career/recommendations",
      {
        method: "POST",
        body: JSON.stringify(input),
      }
    );
  }

  async getCareerInsights(
    input: CareerInsightsInput
  ): Promise<ApiResponse<CareerInsightsOutput>> {
    return this.request<CareerInsightsOutput>("/career/insights", {
      method: "POST",
      body: JSON.stringify(input),
    });
  }

  async healthCheck(): Promise<ApiResponse> {
    return this.request("/health");
  }

  async getUserTypes(): Promise<ApiResponse<UserType[]>> {
    return this.request<UserType[]>("/questions/user-types");
  }

  async getQuestionSections(
    userType: UserType
  ): Promise<ApiResponse<Section[]>> {
    return this.request<Section[]>(`/questions/sections?userType=${userType}`);
  }

  async getQuestionSection(
    sectionId: string,
    userType: UserType
  ): Promise<ApiResponse<Section>> {
    return this.request<Section>(
      `/questions/section/${sectionId}?userType=${userType}`
    );
  }

  async validateAnswers(input: AssessmentInput): Promise<ApiResponse<any>> {
    return this.request<any>("/questions/validate", {
      method: "POST",
      body: JSON.stringify(input),
    });
  }

  async processAssessment(
    input: AssessmentInput
  ): Promise<ApiResponse<CareerPathRecommendationsOutput>> {
    return this.request<CareerPathRecommendationsOutput>("/career/assessment", {
      method: "POST",
      body: JSON.stringify(input),
    });
  }
}

export const apiClient = new ApiClient();

// Legacy function wrappers for compatibility
export async function getCareerRecommendationsAction(
  input: CareerPathRecommendationsInput
): Promise<{
  success: boolean;
  data?: CareerPathRecommendationsOutput;
  error?: string;
}> {
  try {
    const response = await apiClient.getCareerRecommendations(input);
    return {
      success: response.success,
      data: response.data,
      error: response.error,
    };
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return {
      success: false,
      error: `Failed to get career recommendations: ${errorMessage}`,
    };
  }
}

export async function getCareerInsightsAction(
  careerPath: string
): Promise<{ success: boolean; data?: CareerInsightsOutput; error?: string }> {
  try {
    const response = await apiClient.getCareerInsights({ careerPath });
    return {
      success: response.success,
      data: response.data,
      error: response.error,
    };
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return {
      success: false,
      error: `Failed to get career insights: ${errorMessage}`,
    };
  }
}
