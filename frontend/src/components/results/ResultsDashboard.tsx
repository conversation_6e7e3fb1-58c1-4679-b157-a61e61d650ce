'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAssessment } from '@/context/AssessmentContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { Lightbulb, AlertTriangle, ArrowLeft } from 'lucide-react';
import CareerInsights from './CareerInsights';
import type { CareerPathRecommendationsOutput } from '@/ai/flows/career-path-recommendations';

export default function ResultsDashboard() {
  const router = useRouter();
  const { results, isLoading, error, answers, submitAssessment } = useAssessment();
  const [selectedCareer, setSelectedCareer] = useState<string | null>(null);

  useEffect(() => {
    // If we land here with no results and no answers, something is wrong. Go back.
    if (!results && Object.keys(answers).length === 0) {
      router.replace('/assessment');
      return;
    }
    // If we have answers but no results, it means user just finished or reloaded,
    // so we should trigger the submission.
    if (!results && Object.keys(answers).length > 0) {
      submitAssessment();
    }
  }, [results, answers, router, submitAssessment]);

  if (isLoading && !results) {
    return (
      <Card>
        <CardHeader className="items-center text-center">
            <Skeleton className="h-10 w-10 rounded-full bg-primary/20" />
            <CardTitle className="font-headline">Analyzing Your Profile</CardTitle>
            <CardDescription>Our AI is processing your answers to find the best career paths for you. This might take a moment.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-20 w-full" />
          <Skeleton className="h-8 w-3/4" />
          <Skeleton className="h-20 w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Analysis Failed</AlertTitle>
        <AlertDescription>
          {error} Please try again later or restart the assessment.
        </AlertDescription>
        <Button variant="secondary" onClick={() => router.push('/assessment')} className="mt-4">
          <ArrowLeft className="mr-2 h-4 w-4" /> Go Back to Assessment
        </Button>
      </Alert>
    );
  }

  if (!results) {
    return null; // Should be covered by loading/error states or redirect
  }

  const { careerRecommendations, reasoning } = results;

  return (
    <div className="grid md:grid-cols-3 gap-8 items-start">
      <div className="md:col-span-1 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="font-headline">Top Recommendations</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {careerRecommendations.map((career) => (
              <Button
                key={career}
                variant={selectedCareer === career ? 'default' : 'outline'}
                className="w-full justify-start"
                onClick={() => setSelectedCareer(career)}
              >
                {career}
              </Button>
            ))}
          </CardContent>
        </Card>
        <Card>
            <CardHeader className="flex-row items-center gap-2 space-y-0">
                <Lightbulb className="h-6 w-6 text-primary" />
                <CardTitle className="font-headline text-lg">AI Reasoning</CardTitle>
            </CardHeader>
            <CardContent>
                <p className="text-sm text-muted-foreground">{reasoning}</p>
            </CardContent>
        </Card>
      </div>

      <div className="md:col-span-2">
        <CareerInsights selectedCareer={selectedCareer} />
      </div>
    </div>
  );
}
