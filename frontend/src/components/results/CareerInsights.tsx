'use client';

import { useEffect, useState } from 'react';
import { useAssessment } from '@/context/AssessmentContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertTriangle, Briefcase, TrendingUp, ShieldAlert } from 'lucide-react';
import type { CareerInsightsOutput } from '@/ai/flows/career-insights';

type CareerInsightsProps = {
  selectedCareer: string | null;
};

export default function CareerInsights({ selectedCareer }: CareerInsightsProps) {
  const { getInsights } = useAssessment();
  const [insights, setInsights] = useState<CareerInsightsOutput | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (selectedCareer) {
      const fetchInsights = async () => {
        setIsLoading(true);
        setError(null);
        setInsights(null);
        const data = await getInsights(selectedCareer);
        if (data) {
          setInsights(data);
        } else {
          setError('Could not load insights for this career.');
        }
        setIsLoading(false);
      };
      fetchInsights();
    }
  }, [selectedCareer, getInsights]);

  if (!selectedCareer) {
    return (
      <Card className="flex flex-col items-center justify-center text-center h-full min-h-[400px]">
        <CardHeader>
          <div className="mx-auto bg-secondary p-3 rounded-full">
            <Briefcase className="h-8 w-8 text-muted-foreground" />
          </div>
          <CardTitle className="mt-4 font-headline">Explore Your Future</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Select a career recommendation from the list to see detailed insights.</p>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
        <CardContent className="space-y-6">
            <div className="space-y-2">
                <Skeleton className="h-6 w-1/4" />
                <Skeleton className="h-20 w-full" />
            </div>
            <div className="space-y-2">
                <Skeleton className="h-6 w-1/4" />
                <Skeleton className="h-20 w-full" />
            </div>
            <div className="space-y-2">
                <Skeleton className="h-6 w-1/4" />
                <Skeleton className="h-20 w-full" />
            </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!insights) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="font-headline text-3xl">{selectedCareer}</CardTitle>
        <CardDescription>An in-depth look at what a career in {selectedCareer} entails.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h3 className="font-semibold flex items-center mb-2"><TrendingUp className="mr-2 h-5 w-5 text-primary" /> Future Outlook</h3>
          <p className="text-muted-foreground">{insights.futureOutlook}</p>
        </div>
        <div>
          <h3 className="font-semibold flex items-center mb-2"><Briefcase className="mr-2 h-5 w-5 text-primary" /> Career Summary</h3>
          <p className="text-muted-foreground">{insights.summary}</p>
        </div>
        <div>
          <h3 className="font-semibold flex items-center mb-2"><ShieldAlert className="mr-2 h-5 w-5 text-primary" /> Potential Challenges</h3>
          <p className="text-muted-foreground">{insights.potentialChallenges}</p>
        </div>
      </CardContent>
    </Card>
  );
}
