'use client';

import { useState } from 'react';
import { UserType } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { GraduationCap, Briefcase } from 'lucide-react';
import { useAssessment } from '@/context/AssessmentContext';

interface UserTypeSelectionProps {
  onUserTypeSelected: (userType: UserType) => void;
}

export function UserTypeSelection({ onUserTypeSelected }: UserTypeSelectionProps) {
  const [selectedType, setSelectedType] = useState<UserType | null>(null);
  const { loadSections, isLoading } = useAssessment();

  const handleSelectUserType = async (userType: UserType) => {
    setSelectedType(userType);
    try {
      await loadSections(userType);
      onUserTypeSelected(userType);
    } catch (error) {
      console.error('Failed to load sections:', error);
      setSelectedType(null);
    }
  };

  const userTypes = [
    {
      type: UserType.STUDENT,
      title: 'Student',
      description: 'I am currently a student looking for career guidance and educational path recommendations.',
      icon: <GraduationCap className="h-12 w-12 text-blue-500" />,
      features: [
        'Educational path recommendations',
        'Entry-level career opportunities',
        'Skill development guidance',
        'Academic performance analysis'
      ]
    },
    {
      type: UserType.PROFESSIONAL,
      title: 'Working Professional',
      description: 'I am a working professional seeking career advancement or transition opportunities.',
      icon: <Briefcase className="h-12 w-12 text-green-500" />,
      features: [
        'Career transition guidance',
        'Leadership development',
        'Skill advancement recommendations',
        'Professional growth strategies'
      ]
    }
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Choose Your Profile
        </h1>
        <p className="text-lg text-gray-600">
          Select the option that best describes your current situation to get personalized career recommendations.
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {userTypes.map((userTypeOption) => (
          <Card 
            key={userTypeOption.type}
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
              selectedType === userTypeOption.type 
                ? 'ring-2 ring-blue-500 shadow-lg' 
                : 'hover:shadow-md'
            }`}
          >
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                {userTypeOption.icon}
              </div>
              <CardTitle className="text-xl">{userTypeOption.title}</CardTitle>
              <CardDescription className="text-sm">
                {userTypeOption.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 mb-6">
                {userTypeOption.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm text-gray-600">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                    {feature}
                  </li>
                ))}
              </ul>
              <Button
                onClick={() => handleSelectUserType(userTypeOption.type)}
                disabled={isLoading}
                className="w-full"
                variant={selectedType === userTypeOption.type ? "default" : "outline"}
              >
                {isLoading && selectedType === userTypeOption.type 
                  ? 'Loading Questions...' 
                  : `Select ${userTypeOption.title}`
                }
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {selectedType && (
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600">
            You selected: <span className="font-semibold">{selectedType === UserType.STUDENT ? 'Student' : 'Working Professional'}</span>
          </p>
        </div>
      )}
    </div>
  );
}
