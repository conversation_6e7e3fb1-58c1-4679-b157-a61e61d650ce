'use client';

import type { Question } from '@/lib/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

type QuestionCardProps = {
  question: Question;
  value: any;
  onValueChange: (value: any) => void;
  questionNumber: number;
};

export default function QuestionCard({ question, value, onValueChange, questionNumber }: QuestionCardProps) {
  const renderQuestion = () => {
    switch (question.type) {
      case 'short-answer':
        return <Textarea placeholder="Your answer..." value={value || ''} onChange={(e) => onValueChange(e.target.value)} />;
      case 'mcq':
        return (
          <RadioGroup value={value} onValueChange={onValueChange}>
            {question.options?.map((option) => (
              <div key={option} className="flex items-center space-x-2">
                <RadioGroupItem value={option} id={`${question.id}-${option}`} />
                <Label htmlFor={`${question.id}-${option}`}>{option}</Label>
              </div>
            ))}
          </RadioGroup>
        );
      case 'checkbox':
        return (
          <div className="space-y-2">
            {question.options?.map((option) => (
              <div key={option} className="flex items-center space-x-2">
                <Checkbox
                  id={`${question.id}-${option}`}
                  checked={value?.includes(option) || false}
                  onCheckedChange={(checked) => {
                    const currentValues = value || [];
                    if (checked) {
                      onValueChange([...currentValues, option]);
                    } else {
                      onValueChange(currentValues.filter((v: string) => v !== option));
                    }
                  }}
                />
                <Label htmlFor={`${question.id}-${option}`}>{option}</Label>
              </div>
            ))}
          </div>
        );
      case 'yes-no':
        return (
          <RadioGroup value={value} onValueChange={onValueChange} className="flex space-x-4">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="Yes" id={`${question.id}-yes`} />
              <Label htmlFor={`${question.id}-yes`}>Yes</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="No" id={`${question.id}-no`} />
              <Label htmlFor={`${question.id}-no`}>No</Label>
            </div>
          </RadioGroup>
        );
      case 'scale':
        return (
            <div className="flex items-center space-x-4">
                <Slider
                    min={1}
                    max={question.options?.length || 5}
                    step={1}
                    value={[value || 3]}
                    onValueChange={(val) => onValueChange(val[0])}
                />
                <span className="w-20 text-center font-semibold">{question.options?.[(value || 3) - 1] || value}</span>
            </div>
        );
      case 'rating':
        return (
            <div className="space-y-4">
                {question.subQuestions?.map(sub => (
                    <div key={sub} className="space-y-2">
                        <Label>{sub}</Label>
                        <div className="flex items-center space-x-4">
                            <Slider
                                min={1}
                                max={5}
                                step={1}
                                value={[value?.[sub] || 3]}
                                onValueChange={v => onValueChange({...value, [sub]: v[0]})}
                             />
                             <span className="w-8 text-center font-semibold">{value?.[sub] || 3}</span>
                        </div>
                    </div>
                ))}
            </div>
        );
      case 'rank':
        const ranks = ['1st', '2nd', '3rd'];
        return (
          <div className="space-y-2">
            {question.options?.map((option) => (
              <div key={option} className="flex items-center justify-between">
                <Label>{option}</Label>
                <Select
                  value={value?.[option] || ''}
                  onValueChange={(rank) => {
                    const newRanks = { ...(value || {}) };
                    // Clear previous holder of this rank
                    Object.keys(newRanks).forEach(key => {
                      if (newRanks[key] === rank) delete newRanks[key];
                    });
                    newRanks[option] = rank;
                    onValueChange(newRanks);
                  }}
                >
                  <SelectTrigger className="w-[120px]">
                    <SelectValue placeholder="Rank..." />
                  </SelectTrigger>
                  <SelectContent>
                    {ranks.map(rank => (
                      <SelectItem key={rank} value={rank}>{rank}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ))}
          </div>
        );
      default:
        return <Input value={value || ''} onChange={(e) => onValueChange(e.target.value)} />;
    }
  };

  return (
    <div className="space-y-2 rounded-lg border p-4">
      <Label className="font-semibold text-md">{questionNumber}. {question.text}</Label>
      <div className="pt-2">{renderQuestion()}</div>
    </div>
  );
}
