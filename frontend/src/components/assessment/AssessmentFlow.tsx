"use client";

import { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import type { Section } from "@/lib/types";
import { useAssessment } from "@/context/AssessmentContext";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Loader2, ArrowLeft, ArrowRight, RotateCcw } from "lucide-react";
import QuestionCard from "./QuestionCard";

type AssessmentFlowProps = {
  sections: Section[];
};

export default function AssessmentFlow({ sections }: AssessmentFlowProps) {
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { answers, setAnswer, submitAssessment, isLoading, clearProgress } =
    useAssessment();
  const router = useRouter();

  const currentSection = sections[currentSectionIndex];
  const progressValue = ((currentSectionIndex + 1) / sections.length) * 100;

  const handleNext = () => {
    if (currentSectionIndex < sections.length - 1) {
      setCurrentSectionIndex(currentSectionIndex + 1);
    }
  };

  const handleBack = () => {
    if (currentSectionIndex > 0) {
      setCurrentSectionIndex(currentSectionIndex - 1);
    }
  };

  const handleSubmit = async () => {
    await submitAssessment();
    router.push("/results");
  };

  const isLastStep = useMemo(
    () => currentSectionIndex === sections.length - 1,
    [currentSectionIndex, sections.length]
  );

  return (
    <div className="space-y-8">
      <div>
        <div className="flex justify-between mb-2">
          <h2 className="text-2xl font-headline">{currentSection.title}</h2>
          <p className="text-muted-foreground">{`Step ${
            currentSectionIndex + 1
          } of ${sections.length}`}</p>
        </div>
        <p className="text-muted-foreground">{currentSection.description}</p>
        <Progress value={progressValue} className="mt-4" />
      </div>

      <div className="space-y-6">
        {currentSection.questions.map((question, index) => (
          <QuestionCard
            key={question.id}
            question={question}
            value={answers[question.id]}
            onValueChange={(value) => setAnswer(question.id, value)}
            questionNumber={index + 1}
          />
        ))}
      </div>

      <div className="flex justify-between items-center pt-8 border-t">
        <div>
          <Button
            variant="outline"
            onClick={handleBack}
            disabled={currentSectionIndex === 0 || isLoading}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={clearProgress}
            disabled={isLoading}
          >
            <RotateCcw className="mr-2 h-4 w-4" />
            Reset
          </Button>
          {isLastStep ? (
            <Button onClick={handleSubmit} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Analyzing...
                </>
              ) : (
                "Finish & See Results"
              )}
            </Button>
          ) : (
            <Button onClick={handleNext} disabled={isLoading}>
              Next
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
