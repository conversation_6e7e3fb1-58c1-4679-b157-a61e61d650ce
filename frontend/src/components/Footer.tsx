import Link from "next/link";
import { Compass } from 'lucide-react';

export default function Footer() {
  return (
    <footer className="border-t">
      <div className="container flex flex-col items-center justify-between gap-4 py-10 md:h-24 md:flex-row md:py-0">
        <div className="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
          <Compass className="h-6 w-6 text-primary" />
          <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
            Built by your friendly AI assistant.
          </p>
        </div>
        <div className="flex items-center gap-4">
            <p className="text-sm text-muted-foreground">© {new Date().getFullYear()} Career Compass</p>
        </div>
      </div>
    </footer>
  );
}
