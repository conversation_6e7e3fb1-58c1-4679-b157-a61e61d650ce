import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import Image from 'next/image';
import Link from 'next/link';

const resources = [
  {
    title: 'How to Choose a Career Path',
    description: 'A comprehensive guide to making one of the most important decisions of your life.',
    type: 'Article',
    imageUrl: 'https://placehold.co/600x400.png',
    link: '#',
    aiHint: 'desk books'
  },
  {
    title: 'Top 10 Fastest-Growing Industries',
    description: 'Discover the industries with the most potential for growth in the next decade.',
    type: 'Video',
    imageUrl: 'https://placehold.co/600x400.png',
    link: '#',
    aiHint: 'city skyline'
  },
  {
    title: 'Mastering the Art of the Interview',
    description: 'Tips and tricks from hiring managers to help you ace your next job interview.',
    type: 'Article',
    imageUrl: 'https://placehold.co/600x400.png',
    link: '#',
    aiHint: 'job interview'
  },
  {
    title: 'Building a Standout Resume',
    description: 'Learn how to craft a resume that gets noticed by recruiters and lands you interviews.',
    type: 'Video',
    imageUrl: 'https://placehold.co/600x400.png',
    link: '#',
    aiHint: 'resume paper'
  },
   {
    title: 'The Future of Work: AI and Automation',
    description: 'Understand how emerging technologies are shaping the jobs of tomorrow.',
    type: 'Article',
    imageUrl: 'https://placehold.co/600x400.png',
    link: '#',
    aiHint: 'robot arm'
  },
  {
    title: 'Networking for Introverts',
    description: 'Actionable strategies for building professional connections, even if you\'re shy.',
    type: 'Video',
    imageUrl: 'https://placehold.co/600x400.png',
    link: '#',
    aiHint: 'people talking'
  }
];

export default function ResourcesPage() {
  return (
    <div className="container mx-auto py-12 px-4">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-headline font-bold">Resource Library</h1>
        <p className="text-muted-foreground mt-2 max-w-2xl mx-auto">
          Curated articles, videos, and tools to help you on your career journey.
        </p>
      </div>
      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        {resources.map((resource) => (
          <Link key={resource.title} href={resource.link} className="block group">
            <Card className="h-full overflow-hidden transition-all group-hover:shadow-lg group-hover:-translate-y-1">
              <Image
                src={resource.imageUrl}
                alt={resource.title}
                width={600}
                height={400}
                className="w-full h-48 object-cover"
                data-ai-hint={resource.aiHint}
              />
              <CardHeader>
                <CardTitle className="font-headline">{resource.title}</CardTitle>
                <span className="text-xs font-semibold uppercase text-primary">{resource.type}</span>
              </CardHeader>
              <CardContent>
                <CardDescription>{resource.description}</CardDescription>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}
