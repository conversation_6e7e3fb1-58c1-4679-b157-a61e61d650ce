"use client";

import AssessmentFlow from "@/components/assessment/AssessmentFlow";
import { UserTypeSelection } from "@/components/UserTypeSelection";
import { useAssessment } from "@/context/AssessmentContext";
import { UserType } from "@/lib/types";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function AssessmentPage() {
  const { userType, sections } = useAssessment();

  const handleUserTypeSelected = (selectedUserType: UserType) => {
    // The loadSections is already called in UserTypeSelection component
    console.log("User type selected:", selectedUserType);
  };

  return (
    <div className="container mx-auto max-w-4xl py-12 px-4">
      {!userType || sections.length === 0 ? (
        <UserTypeSelection onUserTypeSelected={handleUserTypeSelected} />
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="text-3xl font-headline text-center">
              Career Compass Assessment
            </CardTitle>
            <CardDescription className="text-center">
              Answer the following questions to discover career paths tailored
              to you.
              <br />
              <span className="font-semibold">
                Profile:{" "}
                {userType === UserType.STUDENT
                  ? "Student"
                  : "Working Professional"}
              </span>
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AssessmentFlow sections={sections} />
          </CardContent>
        </Card>
      )}
    </div>
  );
}
