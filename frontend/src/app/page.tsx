import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { CheckCircle, BrainCircuit, LineChart, Library } from 'lucide-react';
import Link from 'next/link';

const features = [
  {
    icon: <CheckCircle className="h-8 w-8 text-primary" />,
    title: 'Dynamic Questionnaire',
    description: 'Answer questions about your interests, personality, and skills to build a comprehensive profile.',
  },
  {
    icon: <BrainCircuit className="h-8 w-8 text-primary" />,
    title: 'AI-Powered Recommendations',
    description: 'Receive personalized career path suggestions based on our advanced AI analysis.',
  },
  {
    icon: <LineChart className="h-8 w-8 text-primary" />,
    title: 'In-Depth Career Insights',
    description: 'Explore future outlooks, potential challenges, and summaries for each recommended career.',
  },
  {
    icon: <Library className="h-8 w-8 text-primary" />,
    title: 'Resource Library',
    description: 'Access curated articles, videos, and courses to support your career exploration journey.',
  },
];

export default function Home() {
  return (
    <div className="flex flex-col items-center">
      <section className="w-full py-20 md:py-32 bg-card border-b">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
            <div className="space-y-4">
              <h1 className="text-4xl font-headline font-bold tracking-tighter sm:text-5xl md:text-6xl text-primary-foreground">
                Find Your Future with Career Compass
              </h1>
              <p className="max-w-[600px] text-muted-foreground md:text-xl">
                Our AI-powered assessment helps you discover career paths that match your unique personality, interests, and skills. Start your journey to a fulfilling career today.
              </p>
              <div className="flex flex-col gap-2 min-[400px]:flex-row">
                <Button asChild size="lg">
                  <Link href="/assessment">Start Your Free Assessment</Link>
                </Button>
              </div>
            </div>
            <div className="flex justify-center">
                <svg
                    className="w-full max-w-md"
                    viewBox="0 0 200 200"
                    xmlns="http://www.w3.org/2000/svg"
                    aria-label="An abstract illustration representing guidance and direction."
                >
                    <path
                        fill="hsl(var(--primary))"
                        d="M48.4,-67.9C61,-57.1,68.4,-42,72.6,-26.2C76.8,-10.3,77.8,6.4,72.2,20.4C66.6,34.4,54.4,45.8,41.2,55.3C28,64.8,14,72.5,-1.2,74C-16.4,75.5,-32.8,70.8,-46.4,62.2C-60,53.6,-70.8,41.1,-75.9,26.7C-81,12.3,-80.4,-4,-74.8,-18.2C-69.2,-32.4,-58.6,-44.6,-46.5,-55.1C-34.4,-65.6,-20.8,-74.5,-5.5,-72.9C9.8,-71.4,19.6,-59.4,29.8,-52.3C40.1,-45.3,50.8,-43.3,48.4,-67.9"
                        transform="translate(100 100) scale(1.2)"
                    ></path>
                </svg>
            </div>
          </div>
        </div>
      </section>
      <section id="features" className="w-full py-20 md:py-32">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <div className="inline-block rounded-lg bg-secondary px-3 py-1 text-sm">
                Key Features
              </div>
              <h2 className="text-3xl font-headline font-bold tracking-tighter sm:text-5xl">
                Your Personal Career Guide
              </h2>
              <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                We provide a comprehensive suite of tools to help you navigate the complex world of career choices with confidence and clarity.
              </p>
            </div>
          </div>
          <div className="mx-auto grid max-w-5xl items-start gap-8 sm:grid-cols-2 md:gap-12 lg:max-w-none lg:grid-cols-4 mt-12">
            {features.map((feature) => (
              <Card key={feature.title} className="h-full">
                <CardHeader className="flex flex-col items-center text-center">
                  {feature.icon}
                  <CardTitle className="mt-4 font-headline text-2xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent className="text-center text-muted-foreground">
                  {feature.description}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
