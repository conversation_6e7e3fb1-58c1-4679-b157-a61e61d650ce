"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useCallback,
} from "react";
import { Answers, UserType, Section } from "@/lib/types";
import type { CareerPathRecommendationsOutput } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { apiClient } from "@/lib/api";

interface AssessmentContextType {
  userType: UserType | null;
  setUserType: (userType: UserType) => void;
  sections: Section[];
  loadSections: (userType: UserType) => Promise<void>;
  answers: Answers;
  setAnswer: (id: string, value: any) => void;
  results: CareerPathRecommendationsOutput | null;
  setResults: (results: CareerPathRecommendationsOutput | null) => void;
  isLoading: boolean;
  error: string | null;
  submitAssessment: () => Promise<void>;
  clearProgress: () => void;
  getInsights: (career: string) => Promise<any>;
}

const AssessmentContext = createContext<AssessmentContextType | undefined>(
  undefined
);

export const AssessmentProvider = ({ children }: { children: ReactNode }) => {
  const [userType, setUserType] = useState<UserType | null>(null);
  const [sections, setSections] = useState<Section[]>([]);
  const [answers, setAnswers] = useState<Answers>({});
  const [results, setResults] =
    useState<CareerPathRecommendationsOutput | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    try {
      const storedAnswers = localStorage.getItem("careerCompassAnswers");
      if (storedAnswers) {
        setAnswers(JSON.parse(storedAnswers));
      }
      const storedResults = localStorage.getItem("careerCompassResults");
      if (storedResults) {
        setResults(JSON.parse(storedResults));
      }
    } catch (e) {
      console.error("Failed to load from localStorage", e);
      toast({
        title: "Error",
        description: "Could not load your previous session.",
        variant: "destructive",
      });
    }
  }, [toast]);

  const loadSections = useCallback(
    async (selectedUserType: UserType) => {
      try {
        setIsLoading(true);
        const response = await apiClient.getQuestionSections(selectedUserType);
        if (response.success && response.data) {
          setSections(response.data);
          setUserType(selectedUserType);
        } else {
          throw new Error(response.error || "Failed to load questions");
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to load questions";
        setError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    },
    [toast]
  );

  const setAnswer = useCallback(
    (id: string, value: any) => {
      const newAnswers = { ...answers, [id]: value };
      setAnswers(newAnswers);
      try {
        localStorage.setItem(
          "careerCompassAnswers",
          JSON.stringify(newAnswers)
        );
      } catch (e) {
        console.error("Failed to save answers to localStorage", e);
      }
    },
    [answers]
  );

  const mapSectionIdToInputKey = (sectionId: string): string => {
    const map: { [key: string]: string } = {
      interest_subject_preferences: "interestAndSubjectPreferences",
      personality_learning_style: "personalityAndLearningStyle",
      skills_strengths: "skillsAndStrengths",
      career_aspirations_work_preferences:
        "careerAspirationsAndWorkPreferences",
      academic_background_performance: "academicBackgroundAndPerformance",
    };
    return map[sectionId] || sectionId;
  };

  const submitAssessment = useCallback(async () => {
    if (!userType) {
      setError("Please select a user type first");
      return;
    }

    // Prevent multiple submissions
    if (isLoading) {
      console.log("Assessment submission already in progress, skipping...");
      return;
    }

    console.log("Starting assessment submission...");
    setIsLoading(true);
    setError(null);
    try {
      const assessmentInput = {
        userType,
        answers,
      };

      console.log("Sending assessment to backend:", assessmentInput);
      const response = await apiClient.processAssessment(assessmentInput);

      if (response.success && response.data) {
        setResults(response.data);
        localStorage.setItem(
          "careerCompassResults",
          JSON.stringify(response.data)
        );
        toast({
          title: "Success!",
          description: "Your career recommendations are ready.",
        });
      } else {
        throw new Error(response.error || "An unknown error occurred.");
      }
    } catch (e: any) {
      setError(e.message || "Failed to submit assessment.");
      toast({
        title: "Error",
        description: e.message || "Failed to submit assessment.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [userType, answers, isLoading, toast]);

  const clearProgress = () => {
    setAnswers({});
    setResults(null);
    try {
      localStorage.removeItem("careerCompassAnswers");
      localStorage.removeItem("careerCompassResults");
      toast({
        title: "Progress Cleared",
        description: "Your assessment has been reset.",
      });
    } catch (e) {
      console.error("Failed to clear localStorage", e);
    }
  };

  const getInsights = useCallback(
    async (career: string) => {
      setIsLoading(true);
      try {
        const response = await apiClient.getCareerInsights({
          careerPath: career,
        });
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(response.error || "An unknown error occurred.");
      } catch (e: any) {
        setError(e.message);
        toast({
          title: "Error fetching insights",
          description: e.message,
          variant: "destructive",
        });
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [toast]
  );

  return (
    <AssessmentContext.Provider
      value={{
        userType,
        setUserType,
        sections,
        loadSections,
        answers,
        setAnswer,
        results,
        setResults,
        isLoading,
        error,
        submitAssessment,
        clearProgress,
        getInsights,
      }}
    >
      {children}
    </AssessmentContext.Provider>
  );
};

export const useAssessment = () => {
  const context = useContext(AssessmentContext);
  if (context === undefined) {
    throw new Error("useAssessment must be used within an AssessmentProvider");
  }
  return context;
};
