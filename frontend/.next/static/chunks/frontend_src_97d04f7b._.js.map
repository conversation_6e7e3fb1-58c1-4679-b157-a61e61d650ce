{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Textarea = React.forwardRef<\n  HTMLTextAreaElement,\n  React.ComponentProps<\"textarea\">\n>(({ className, ...props }, ref) => {\n  return (\n    <textarea\n      className={cn(\n        \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      ref={ref}\n      {...props}\n    />\n  )\n})\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\nimport { Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst RadioGroup = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Root\n      className={cn(\"grid gap-2\", className)}\n      {...props}\n      ref={ref}\n    />\n  )\n})\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\n\nconst RadioGroupItem = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Item\n      ref={ref}\n      className={cn(\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\n      </RadioGroupPrimitive.Indicator>\n    </RadioGroupPrimitive.Item>\n  )\n})\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\n\nexport { RadioGroup, RadioGroupItem }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;QACT,KAAK;;;;;;AAGX;;AACA,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,4OACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,YAA6B;YAAC,WAAU;sBACvC,cAAA,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;;AACA,eAAe,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Slider = React.forwardRef<\n  React.ElementRef<typeof SliderPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <SliderPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex w-full touch-none select-none items-center\",\n      className\n    )}\n    {...props}\n  >\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\n    </SliderPrimitive.Track>\n    <SliderPrimitive.Thumb className=\"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\n  </SliderPrimitive.Root>\n))\nSlider.displayName = SliderPrimitive.Root.displayName\n\nexport { Slider }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;0BAET,6LAAC,qKAAA,CAAA,QAAqB;gBAAC,WAAU;0BAC/B,cAAA,6LAAC,qKAAA,CAAA,QAAqB;oBAAC,WAAU;;;;;;;;;;;0BAEnC,6LAAC,qKAAA,CAAA,QAAqB;gBAAC,WAAU;;;;;;;;;;;;;AAGrC,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/assessment/QuestionCard.tsx"], "sourcesContent": ["'use client';\n\nimport type { Question } from '@/lib/types';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Label } from '@/components/ui/label';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { Slider } from '@/components/ui/slider';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\n\ntype QuestionCardProps = {\n  question: Question;\n  value: any;\n  onValueChange: (value: any) => void;\n  questionNumber: number;\n};\n\nexport default function QuestionCard({ question, value, onValueChange, questionNumber }: QuestionCardProps) {\n  const renderQuestion = () => {\n    switch (question.type) {\n      case 'short-answer':\n        return <Textarea placeholder=\"Your answer...\" value={value || ''} onChange={(e) => onValueChange(e.target.value)} />;\n      case 'mcq':\n        return (\n          <RadioGroup value={value} onValueChange={onValueChange}>\n            {question.options?.map((option) => (\n              <div key={option} className=\"flex items-center space-x-2\">\n                <RadioGroupItem value={option} id={`${question.id}-${option}`} />\n                <Label htmlFor={`${question.id}-${option}`}>{option}</Label>\n              </div>\n            ))}\n          </RadioGroup>\n        );\n      case 'checkbox':\n        return (\n          <div className=\"space-y-2\">\n            {question.options?.map((option) => (\n              <div key={option} className=\"flex items-center space-x-2\">\n                <Checkbox\n                  id={`${question.id}-${option}`}\n                  checked={value?.includes(option) || false}\n                  onCheckedChange={(checked) => {\n                    const currentValues = value || [];\n                    if (checked) {\n                      onValueChange([...currentValues, option]);\n                    } else {\n                      onValueChange(currentValues.filter((v: string) => v !== option));\n                    }\n                  }}\n                />\n                <Label htmlFor={`${question.id}-${option}`}>{option}</Label>\n              </div>\n            ))}\n          </div>\n        );\n      case 'yes-no':\n        return (\n          <RadioGroup value={value} onValueChange={onValueChange} className=\"flex space-x-4\">\n            <div className=\"flex items-center space-x-2\">\n              <RadioGroupItem value=\"Yes\" id={`${question.id}-yes`} />\n              <Label htmlFor={`${question.id}-yes`}>Yes</Label>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <RadioGroupItem value=\"No\" id={`${question.id}-no`} />\n              <Label htmlFor={`${question.id}-no`}>No</Label>\n            </div>\n          </RadioGroup>\n        );\n      case 'scale':\n        return (\n            <div className=\"flex items-center space-x-4\">\n                <Slider\n                    min={1}\n                    max={question.options?.length || 5}\n                    step={1}\n                    value={[value || 3]}\n                    onValueChange={(val) => onValueChange(val[0])}\n                />\n                <span className=\"w-20 text-center font-semibold\">{question.options?.[(value || 3) - 1] || value}</span>\n            </div>\n        );\n      case 'rating':\n        return (\n            <div className=\"space-y-4\">\n                {question.subQuestions?.map(sub => (\n                    <div key={sub} className=\"space-y-2\">\n                        <Label>{sub}</Label>\n                        <div className=\"flex items-center space-x-4\">\n                            <Slider\n                                min={1}\n                                max={5}\n                                step={1}\n                                value={[value?.[sub] || 3]}\n                                onValueChange={v => onValueChange({...value, [sub]: v[0]})}\n                             />\n                             <span className=\"w-8 text-center font-semibold\">{value?.[sub] || 3}</span>\n                        </div>\n                    </div>\n                ))}\n            </div>\n        );\n      case 'rank':\n        const ranks = ['1st', '2nd', '3rd'];\n        return (\n          <div className=\"space-y-2\">\n            {question.options?.map((option) => (\n              <div key={option} className=\"flex items-center justify-between\">\n                <Label>{option}</Label>\n                <Select\n                  value={value?.[option] || ''}\n                  onValueChange={(rank) => {\n                    const newRanks = { ...(value || {}) };\n                    // Clear previous holder of this rank\n                    Object.keys(newRanks).forEach(key => {\n                      if (newRanks[key] === rank) delete newRanks[key];\n                    });\n                    newRanks[option] = rank;\n                    onValueChange(newRanks);\n                  }}\n                >\n                  <SelectTrigger className=\"w-[120px]\">\n                    <SelectValue placeholder=\"Rank...\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {ranks.map(rank => (\n                      <SelectItem key={rank} value={rank}>{rank}</SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n            ))}\n          </div>\n        );\n      default:\n        return <Input value={value || ''} onChange={(e) => onValueChange(e.target.value)} />;\n    }\n  };\n\n  return (\n    <div className=\"space-y-2 rounded-lg border p-4\">\n      <Label className=\"font-semibold text-md\">{questionNumber}. {question.text}</Label>\n      <div className=\"pt-2\">{renderQuestion()}</div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;AAmBe,SAAS,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,cAAc,EAAqB;IACxG,MAAM,iBAAiB;QACrB,OAAQ,SAAS,IAAI;YACnB,KAAK;gBACH,qBAAO,6LAAC,mJAAA,CAAA,WAAQ;oBAAC,aAAY;oBAAiB,OAAO,SAAS;oBAAI,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;YACjH,KAAK;gBACH,qBACE,6LAAC,yJAAA,CAAA,aAAU;oBAAC,OAAO;oBAAO,eAAe;8BACtC,SAAS,OAAO,EAAE,IAAI,CAAC,uBACtB,6LAAC;4BAAiB,WAAU;;8CAC1B,6LAAC,yJAAA,CAAA,iBAAc;oCAAC,OAAO;oCAAQ,IAAI,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,QAAQ;;;;;;8CAC7D,6LAAC,gJAAA,CAAA,QAAK;oCAAC,SAAS,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,QAAQ;8CAAG;;;;;;;2BAFrC;;;;;;;;;;YAOlB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACZ,SAAS,OAAO,EAAE,IAAI,CAAC,uBACtB,6LAAC;4BAAiB,WAAU;;8CAC1B,6LAAC,mJAAA,CAAA,WAAQ;oCACP,IAAI,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,QAAQ;oCAC9B,SAAS,OAAO,SAAS,WAAW;oCACpC,iBAAiB,CAAC;wCAChB,MAAM,gBAAgB,SAAS,EAAE;wCACjC,IAAI,SAAS;4CACX,cAAc;mDAAI;gDAAe;6CAAO;wCAC1C,OAAO;4CACL,cAAc,cAAc,MAAM,CAAC,CAAC,IAAc,MAAM;wCAC1D;oCACF;;;;;;8CAEF,6LAAC,gJAAA,CAAA,QAAK;oCAAC,SAAS,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,QAAQ;8CAAG;;;;;;;2BAbrC;;;;;;;;;;YAkBlB,KAAK;gBACH,qBACE,6LAAC,yJAAA,CAAA,aAAU;oBAAC,OAAO;oBAAO,eAAe;oBAAe,WAAU;;sCAChE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yJAAA,CAAA,iBAAc;oCAAC,OAAM;oCAAM,IAAI,GAAG,SAAS,EAAE,CAAC,IAAI,CAAC;;;;;;8CACpD,6LAAC,gJAAA,CAAA,QAAK;oCAAC,SAAS,GAAG,SAAS,EAAE,CAAC,IAAI,CAAC;8CAAE;;;;;;;;;;;;sCAExC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yJAAA,CAAA,iBAAc;oCAAC,OAAM;oCAAK,IAAI,GAAG,SAAS,EAAE,CAAC,GAAG,CAAC;;;;;;8CAClD,6LAAC,gJAAA,CAAA,QAAK;oCAAC,SAAS,GAAG,SAAS,EAAE,CAAC,GAAG,CAAC;8CAAE;;;;;;;;;;;;;;;;;;YAI7C,KAAK;gBACH,qBACI,6LAAC;oBAAI,WAAU;;sCACX,6LAAC,iJAAA,CAAA,SAAM;4BACH,KAAK;4BACL,KAAK,SAAS,OAAO,EAAE,UAAU;4BACjC,MAAM;4BACN,OAAO;gCAAC,SAAS;6BAAE;4BACnB,eAAe,CAAC,MAAQ,cAAc,GAAG,CAAC,EAAE;;;;;;sCAEhD,6LAAC;4BAAK,WAAU;sCAAkC,SAAS,OAAO,EAAE,CAAC,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI;;;;;;;;;;;;YAGpG,KAAK;gBACH,qBACI,6LAAC;oBAAI,WAAU;8BACV,SAAS,YAAY,EAAE,IAAI,CAAA,oBACxB,6LAAC;4BAAc,WAAU;;8CACrB,6LAAC,gJAAA,CAAA,QAAK;8CAAE;;;;;;8CACR,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,iJAAA,CAAA,SAAM;4CACH,KAAK;4CACL,KAAK;4CACL,MAAM;4CACN,OAAO;gDAAC,OAAO,CAAC,IAAI,IAAI;6CAAE;4CAC1B,eAAe,CAAA,IAAK,cAAc;oDAAC,GAAG,KAAK;oDAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;gDAAA;;;;;;sDAE3D,6LAAC;4CAAK,WAAU;sDAAiC,OAAO,CAAC,IAAI,IAAI;;;;;;;;;;;;;2BAVhE;;;;;;;;;;YAgBxB,KAAK;gBACH,MAAM,QAAQ;oBAAC;oBAAO;oBAAO;iBAAM;gBACnC,qBACE,6LAAC;oBAAI,WAAU;8BACZ,SAAS,OAAO,EAAE,IAAI,CAAC,uBACtB,6LAAC;4BAAiB,WAAU;;8CAC1B,6LAAC,gJAAA,CAAA,QAAK;8CAAE;;;;;;8CACR,6LAAC,iJAAA,CAAA,SAAM;oCACL,OAAO,OAAO,CAAC,OAAO,IAAI;oCAC1B,eAAe,CAAC;wCACd,MAAM,WAAW;4CAAE,GAAI,SAAS,CAAC,CAAC;wCAAE;wCACpC,qCAAqC;wCACrC,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,CAAA;4CAC5B,IAAI,QAAQ,CAAC,IAAI,KAAK,MAAM,OAAO,QAAQ,CAAC,IAAI;wCAClD;wCACA,QAAQ,CAAC,OAAO,GAAG;wCACnB,cAAc;oCAChB;;sDAEA,6LAAC,iJAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,6LAAC,iJAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6LAAC,iJAAA,CAAA,gBAAa;sDACX,MAAM,GAAG,CAAC,CAAA,qBACT,6LAAC,iJAAA,CAAA,aAAU;oDAAY,OAAO;8DAAO;mDAApB;;;;;;;;;;;;;;;;;2BAnBf;;;;;;;;;;YA2BlB;gBACE,qBAAO,6LAAC,gJAAA,CAAA,QAAK;oBAAC,OAAO,SAAS;oBAAI,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;QACnF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,gJAAA,CAAA,QAAK;gBAAC,WAAU;;oBAAyB;oBAAe;oBAAG,SAAS,IAAI;;;;;;;0BACzE,6LAAC;gBAAI,WAAU;0BAAQ;;;;;;;;;;;;AAG7B;KA/HwB", "debugId": null}}, {"offset": {"line": 950, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/assessment/AssessmentFlow.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useMemo } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport type { Section } from \"@/lib/types\";\nimport { useAssessment } from \"@/context/AssessmentContext\";\nimport { Button } from \"@/components/ui/button\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Loader2, ArrowLeft, ArrowRight, RotateCcw } from \"lucide-react\";\nimport QuestionCard from \"./QuestionCard\";\n\ntype AssessmentFlowProps = {\n  sections: Section[];\n};\n\nexport default function AssessmentFlow({ sections }: AssessmentFlowProps) {\n  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const { answers, setAnswer, submitAssessment, isLoading, clearProgress } =\n    useAssessment();\n  const router = useRouter();\n\n  const currentSection = sections[currentSectionIndex];\n  const progressValue = ((currentSectionIndex + 1) / sections.length) * 100;\n\n  const handleNext = () => {\n    if (currentSectionIndex < sections.length - 1) {\n      setCurrentSectionIndex(currentSectionIndex + 1);\n    }\n  };\n\n  const handleBack = () => {\n    if (currentSectionIndex > 0) {\n      setCurrentSectionIndex(currentSectionIndex - 1);\n    }\n  };\n\n  const handleSubmit = async () => {\n    await submitAssessment();\n    router.push(\"/results\");\n  };\n\n  const isLastStep = useMemo(\n    () => currentSectionIndex === sections.length - 1,\n    [currentSectionIndex, sections.length]\n  );\n\n  return (\n    <div className=\"space-y-8\">\n      <div>\n        <div className=\"flex justify-between mb-2\">\n          <h2 className=\"text-2xl font-headline\">{currentSection.title}</h2>\n          <p className=\"text-muted-foreground\">{`Step ${\n            currentSectionIndex + 1\n          } of ${sections.length}`}</p>\n        </div>\n        <p className=\"text-muted-foreground\">{currentSection.description}</p>\n        <Progress value={progressValue} className=\"mt-4\" />\n      </div>\n\n      <div className=\"space-y-6\">\n        {currentSection.questions.map((question, index) => (\n          <QuestionCard\n            key={question.id}\n            question={question}\n            value={answers[question.id]}\n            onValueChange={(value) => setAnswer(question.id, value)}\n            questionNumber={index + 1}\n          />\n        ))}\n      </div>\n\n      <div className=\"flex justify-between items-center pt-8 border-t\">\n        <div>\n          <Button\n            variant=\"outline\"\n            onClick={handleBack}\n            disabled={currentSectionIndex === 0 || isLoading}\n          >\n            <ArrowLeft className=\"mr-2 h-4 w-4\" />\n            Back\n          </Button>\n        </div>\n        <div className=\"flex items-center gap-4\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={clearProgress}\n            disabled={isLoading}\n          >\n            <RotateCcw className=\"mr-2 h-4 w-4\" />\n            Reset\n          </Button>\n          {isLastStep ? (\n            <Button onClick={handleSubmit} disabled={isLoading}>\n              {isLoading ? (\n                <>\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                  Analyzing...\n                </>\n              ) : (\n                \"Finish & See Results\"\n              )}\n            </Button>\n          ) : (\n            <Button onClick={handleNext} disabled={isLoading}>\n              Next\n              <ArrowRight className=\"ml-2 h-4 w-4\" />\n            </Button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AATA;;;;;;;;AAee,SAAS,eAAe,EAAE,QAAQ,EAAuB;;IACtE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,aAAa,EAAE,GACtE,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,iBAAiB,QAAQ,CAAC,oBAAoB;IACpD,MAAM,gBAAgB,AAAC,CAAC,sBAAsB,CAAC,IAAI,SAAS,MAAM,GAAI;IAEtE,MAAM,aAAa;QACjB,IAAI,sBAAsB,SAAS,MAAM,GAAG,GAAG;YAC7C,uBAAuB,sBAAsB;QAC/C;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,sBAAsB,GAAG;YAC3B,uBAAuB,sBAAsB;QAC/C;IACF;IAEA,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CACvB,IAAM,wBAAwB,SAAS,MAAM,GAAG;6CAChD;QAAC;QAAqB,SAAS,MAAM;KAAC;IAGxC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0B,eAAe,KAAK;;;;;;0CAC5D,6LAAC;gCAAE,WAAU;0CAAyB,CAAC,KAAK,EAC1C,sBAAsB,EACvB,IAAI,EAAE,SAAS,MAAM,EAAE;;;;;;;;;;;;kCAE1B,6LAAC;wBAAE,WAAU;kCAAyB,eAAe,WAAW;;;;;;kCAChE,6LAAC,mJAAA,CAAA,WAAQ;wBAAC,OAAO;wBAAe,WAAU;;;;;;;;;;;;0BAG5C,6LAAC;gBAAI,WAAU;0BACZ,eAAe,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBACvC,6LAAC,+JAAA,CAAA,UAAY;wBAEX,UAAU;wBACV,OAAO,OAAO,CAAC,SAAS,EAAE,CAAC;wBAC3B,eAAe,CAAC,QAAU,UAAU,SAAS,EAAE,EAAE;wBACjD,gBAAgB,QAAQ;uBAJnB,SAAS,EAAE;;;;;;;;;;0BAStB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCACC,cAAA,6LAAC,iJAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU,wBAAwB,KAAK;;8CAEvC,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAI1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;4BAGvC,2BACC,6LAAC,iJAAA,CAAA,SAAM;gCAAC,SAAS;gCAAc,UAAU;0CACtC,0BACC;;sDACE,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA8B;;mDAInD;;;;;qDAIJ,6LAAC,iJAAA,CAAA,SAAM;gCAAC,SAAS;gCAAY,UAAU;;oCAAW;kDAEhD,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;GAnGwB;;QAIpB,mJAAA,CAAA,gBAAa;QACA,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 1196, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/lib/types.ts"], "sourcesContent": ["export enum UserType {\n  STUDENT = \"student\",\n  PROFESSIONAL = \"professional\",\n}\n\nexport enum QuestionType {\n  MCQ = \"mcq\",\n  CHECKBOX = \"checkbox\",\n  SHORT_ANSWER = \"short-answer\",\n  SCALE = \"scale\",\n  YES_NO = \"yes-no\",\n  RANKING = \"ranking\",\n}\n\nexport interface Question {\n  id: string;\n  type: QuestionType;\n  text: string;\n  options?: string[];\n  required?: boolean;\n  maxSelections?: number; // For checkbox questions\n  scaleMin?: number; // For scale questions\n  scaleMax?: number; // For scale questions\n  scaleLabels?: string[]; // For scale questions\n}\n\nexport interface Section {\n  id: string;\n  title: string;\n  description: string;\n  questions: Question[];\n  userType: UserType;\n}\n\nexport interface Answers {\n  [key: string]: any;\n}\n\nexport interface AssessmentInput {\n  userType: UserType;\n  answers: Answers;\n}\n"], "names": [], "mappings": ";;;;AAAO,IAAA,AAAK,kCAAA;;;WAAA;;AAKL,IAAA,AAAK,sCAAA;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 1223, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1326, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/UserTypeSelection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { UserType } from '@/lib/types';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { GraduationCap, Briefcase } from 'lucide-react';\nimport { useAssessment } from '@/context/AssessmentContext';\n\ninterface UserTypeSelectionProps {\n  onUserTypeSelected: (userType: UserType) => void;\n}\n\nexport function UserTypeSelection({ onUserTypeSelected }: UserTypeSelectionProps) {\n  const [selectedType, setSelectedType] = useState<UserType | null>(null);\n  const { loadSections, isLoading } = useAssessment();\n\n  const handleSelectUserType = async (userType: UserType) => {\n    setSelectedType(userType);\n    try {\n      await loadSections(userType);\n      onUserTypeSelected(userType);\n    } catch (error) {\n      console.error('Failed to load sections:', error);\n      setSelectedType(null);\n    }\n  };\n\n  const userTypes = [\n    {\n      type: UserType.STUDENT,\n      title: 'Student',\n      description: 'I am currently a student looking for career guidance and educational path recommendations.',\n      icon: <GraduationCap className=\"h-12 w-12 text-blue-500\" />,\n      features: [\n        'Educational path recommendations',\n        'Entry-level career opportunities',\n        'Skill development guidance',\n        'Academic performance analysis'\n      ]\n    },\n    {\n      type: UserType.PROFESSIONAL,\n      title: 'Working Professional',\n      description: 'I am a working professional seeking career advancement or transition opportunities.',\n      icon: <Briefcase className=\"h-12 w-12 text-green-500\" />,\n      features: [\n        'Career transition guidance',\n        'Leadership development',\n        'Skill advancement recommendations',\n        'Professional growth strategies'\n      ]\n    }\n  ];\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6\">\n      <div className=\"text-center mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n          Choose Your Profile\n        </h1>\n        <p className=\"text-lg text-gray-600\">\n          Select the option that best describes your current situation to get personalized career recommendations.\n        </p>\n      </div>\n\n      <div className=\"grid md:grid-cols-2 gap-6\">\n        {userTypes.map((userTypeOption) => (\n          <Card \n            key={userTypeOption.type}\n            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${\n              selectedType === userTypeOption.type \n                ? 'ring-2 ring-blue-500 shadow-lg' \n                : 'hover:shadow-md'\n            }`}\n          >\n            <CardHeader className=\"text-center\">\n              <div className=\"flex justify-center mb-4\">\n                {userTypeOption.icon}\n              </div>\n              <CardTitle className=\"text-xl\">{userTypeOption.title}</CardTitle>\n              <CardDescription className=\"text-sm\">\n                {userTypeOption.description}\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ul className=\"space-y-2 mb-6\">\n                {userTypeOption.features.map((feature, index) => (\n                  <li key={index} className=\"flex items-center text-sm text-gray-600\">\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full mr-3\"></div>\n                    {feature}\n                  </li>\n                ))}\n              </ul>\n              <Button\n                onClick={() => handleSelectUserType(userTypeOption.type)}\n                disabled={isLoading}\n                className=\"w-full\"\n                variant={selectedType === userTypeOption.type ? \"default\" : \"outline\"}\n              >\n                {isLoading && selectedType === userTypeOption.type \n                  ? 'Loading Questions...' \n                  : `Select ${userTypeOption.title}`\n                }\n              </Button>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {selectedType && (\n        <div className=\"mt-8 text-center\">\n          <p className=\"text-sm text-gray-600\">\n            You selected: <span className=\"font-semibold\">{selectedType === UserType.STUDENT ? 'Student' : 'Working Professional'}</span>\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;;;AAPA;;;;;;;AAaO,SAAS,kBAAkB,EAAE,kBAAkB,EAA0B;;IAC9E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAClE,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD;IAEhD,MAAM,uBAAuB,OAAO;QAClC,gBAAgB;QAChB,IAAI;YACF,MAAM,aAAa;YACnB,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,gBAAgB;QAClB;IACF;IAEA,MAAM,YAAY;QAChB;YACE,MAAM,kIAAA,CAAA,WAAQ,CAAC,OAAO;YACtB,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,MAAM,kIAAA,CAAA,WAAQ,CAAC,YAAY;YAC3B,OAAO;YACP,aAAa;YACb,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;QACH;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,+BACd,6LAAC,+IAAA,CAAA,OAAI;wBAEH,WAAW,CAAC,2DAA2D,EACrE,iBAAiB,eAAe,IAAI,GAChC,mCACA,mBACJ;;0CAEF,6LAAC,+IAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC;wCAAI,WAAU;kDACZ,eAAe,IAAI;;;;;;kDAEtB,6LAAC,+IAAA,CAAA,YAAS;wCAAC,WAAU;kDAAW,eAAe,KAAK;;;;;;kDACpD,6LAAC,+IAAA,CAAA,kBAAe;wCAAC,WAAU;kDACxB,eAAe,WAAW;;;;;;;;;;;;0CAG/B,6LAAC,+IAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAG,WAAU;kDACX,eAAe,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACrC,6LAAC;gDAAe,WAAU;;kEACxB,6LAAC;wDAAI,WAAU;;;;;;oDACd;;+CAFM;;;;;;;;;;kDAMb,6LAAC,iJAAA,CAAA,SAAM;wCACL,SAAS,IAAM,qBAAqB,eAAe,IAAI;wCACvD,UAAU;wCACV,WAAU;wCACV,SAAS,iBAAiB,eAAe,IAAI,GAAG,YAAY;kDAE3D,aAAa,iBAAiB,eAAe,IAAI,GAC9C,yBACA,CAAC,OAAO,EAAE,eAAe,KAAK,EAAE;;;;;;;;;;;;;uBAjCnC,eAAe,IAAI;;;;;;;;;;YAyC7B,8BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAAwB;sCACrB,6LAAC;4BAAK,WAAU;sCAAiB,iBAAiB,kIAAA,CAAA,WAAQ,CAAC,OAAO,GAAG,YAAY;;;;;;;;;;;;;;;;;;;;;;;AAM3G;GA1GgB;;QAEsB,mJAAA,CAAA,gBAAa;;;KAFnC", "debugId": null}}, {"offset": {"line": 1567, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/app/assessment/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport AssessmentFlow from \"@/components/assessment/AssessmentFlow\";\nimport { UserTypeSelection } from \"@/components/UserTypeSelection\";\nimport { useAssessment } from \"@/context/AssessmentContext\";\nimport { UserType } from \"@/lib/types\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\n\nexport default function AssessmentPage() {\n  const { userType, sections } = useAssessment();\n\n  const handleUserTypeSelected = (selectedUserType: UserType) => {\n    // The loadSections is already called in UserTypeSelection component\n    console.log(\"User type selected:\", selectedUserType);\n  };\n\n  return (\n    <div className=\"container mx-auto max-w-4xl py-12 px-4\">\n      {!userType || sections.length === 0 ? (\n        <UserTypeSelection onUserTypeSelected={handleUserTypeSelected} />\n      ) : (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-3xl font-headline text-center\">\n              Career Compass Assessment\n            </CardTitle>\n            <CardDescription className=\"text-center\">\n              Answer the following questions to discover career paths tailored\n              to you.\n              <br />\n              <span className=\"font-semibold\">\n                Profile:{\" \"}\n                {userType === UserType.STUDENT\n                  ? \"Student\"\n                  : \"Working Professional\"}\n              </span>\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <AssessmentFlow sections={sections} />\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAce,SAAS;;IACtB,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD;IAE3C,MAAM,yBAAyB,CAAC;QAC9B,oEAAoE;QACpE,QAAQ,GAAG,CAAC,uBAAuB;IACrC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,CAAC,YAAY,SAAS,MAAM,KAAK,kBAChC,6LAAC,sJAAA,CAAA,oBAAiB;YAAC,oBAAoB;;;;;iCAEvC,6LAAC,+IAAA,CAAA,OAAI;;8BACH,6LAAC,+IAAA,CAAA,aAAU;;sCACT,6LAAC,+IAAA,CAAA,YAAS;4BAAC,WAAU;sCAAqC;;;;;;sCAG1D,6LAAC,+IAAA,CAAA,kBAAe;4BAAC,WAAU;;gCAAc;8CAGvC,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;;wCAAgB;wCACrB;wCACR,aAAa,kIAAA,CAAA,WAAQ,CAAC,OAAO,GAC1B,YACA;;;;;;;;;;;;;;;;;;;8BAIV,6LAAC,+IAAA,CAAA,cAAW;8BACV,cAAA,6LAAC,iKAAA,CAAA,UAAc;wBAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;AAMtC;GArCwB;;QACS,mJAAA,CAAA,gBAAa;;;KADtB", "debugId": null}}]}