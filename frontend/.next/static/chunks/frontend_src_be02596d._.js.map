{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/hooks/use-toast.ts"], "sourcesContent": ["\"use client\"\n\n// Inspired by react-hot-toast library\nimport * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"@/components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n"], "names": [], "mappings": ";;;;;AAEA,sCAAsC;AACtC;;AAHA;;AAUA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;8BAAE;YACd,UAAU,IAAI,CAAC;YACf;sCAAO;oBACL,MAAM,QAAQ,UAAU,OAAO,CAAC;oBAChC,IAAI,QAAQ,CAAC,GAAG;wBACd,UAAU,MAAM,CAAC,OAAO;oBAC1B;gBACF;;QACF;6BAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF;GAlBS", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/ui/toast.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ToastProvider = ToastPrimitives.Provider\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\n      className\n    )}\n    {...props}\n  />\n))\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\n\nconst toastVariants = cva(\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\n  {\n    variants: {\n      variant: {\n        default: \"border bg-background text-foreground\",\n        destructive:\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\n    VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return (\n    <ToastPrimitives.Root\n      ref={ref}\n      className={cn(toastVariants({ variant }), className)}\n      {...props}\n    />\n  )\n})\nToast.displayName = ToastPrimitives.Root.displayName\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\n      className\n    )}\n    {...props}\n  />\n))\nToastAction.displayName = ToastPrimitives.Action.displayName\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\n      className\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"h-4 w-4\" />\n  </ToastPrimitives.Close>\n))\nToastClose.displayName = ToastPrimitives.Close.displayName\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title\n    ref={ref}\n    className={cn(\"text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nToastTitle.displayName = ToastPrimitives.Title.displayName\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description\n    ref={ref}\n    className={cn(\"text-sm opacity-90\", className)}\n    {...props}\n  />\n))\nToastDescription.displayName = ToastPrimitives.Description.displayName\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,gBAAgB,oKAAA,CAAA,WAAwB;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;;AAGb,cAAc,WAAW,GAAG,oKAAA,CAAA,WAAwB,CAAC,WAAW;AAEhE,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6lBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAI3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,qBACE,6LAAC,oKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;;AACA,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAoB,CAAC,WAAW;AAEpD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,SAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,sgBACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAA,CAAA,SAAsB,CAAC,WAAW;AAE5D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,yVACA;QAEF,eAAY;QACX,GAAG,KAAK;kBAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;YAAC,WAAU;;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,oKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG,oKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,oKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/ui/toaster.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useToast } from \"@/hooks/use-toast\"\nimport {\n  Toast,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n} from \"@/components/ui/toast\"\n\nexport function Toaster() {\n  const { toasts } = useToast()\n\n  return (\n    <ToastProvider>\n      {toasts.map(function ({ id, title, description, action, ...props }) {\n        return (\n          <Toast key={id} {...props}>\n            <div className=\"grid gap-1\">\n              {title && <ToastTitle>{title}</ToastTitle>}\n              {description && (\n                <ToastDescription>{description}</ToastDescription>\n              )}\n            </div>\n            {action}\n            <ToastClose />\n          </Toast>\n        )\n      })}\n      <ToastViewport />\n    </ToastProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAYO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD;IAE1B,qBACE,6LAAC,gJAAA,CAAA,gBAAa;;YACX,OAAO,GAAG,CAAC,SAAU,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,OAAO;gBAChE,qBACE,6LAAC,gJAAA,CAAA,QAAK;oBAAW,GAAG,KAAK;;sCACvB,6LAAC;4BAAI,WAAU;;gCACZ,uBAAS,6LAAC,gJAAA,CAAA,aAAU;8CAAE;;;;;;gCACtB,6BACC,6LAAC,gJAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAGtB;sCACD,6LAAC,gJAAA,CAAA,aAAU;;;;;;mBARD;;;;;YAWhB;0BACA,6LAAC,gJAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB;GAtBgB;;QACK,2IAAA,CAAA,WAAQ;;;KADb", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/lib/api.ts"], "sourcesContent": ["/**\n * API client for Career Compass Backend\n */\n\nconst API_BASE_URL =\n  process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001/api\";\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nexport interface CareerPathRecommendationsInput {\n  interestAndSubjectPreferences: string;\n  personalityAndLearningStyle: string;\n  skillsAndStrengths: string;\n  careerAspirationsAndWorkPreferences: string;\n  academicBackgroundAndPerformance: string;\n}\n\nexport interface CareerPathRecommendationsOutput {\n  careerRecommendations: string[];\n  reasoning: string;\n}\n\nexport interface CareerInsightsInput {\n  careerPath: string;\n}\n\nexport interface CareerInsightsOutput {\n  summary: string;\n  futureOutlook: string;\n  potentialChallenges: string;\n}\n\nexport enum UserType {\n  STUDENT = \"student\",\n  PROFESSIONAL = \"professional\",\n}\n\nexport enum QuestionType {\n  MCQ = \"mcq\",\n  CHECKBOX = \"checkbox\",\n  SHORT_ANSWER = \"short-answer\",\n  SCALE = \"scale\",\n  YES_NO = \"yes-no\",\n  RANKING = \"ranking\",\n}\n\nexport interface Question {\n  id: string;\n  type: QuestionType;\n  text: string;\n  options?: string[];\n  required?: boolean;\n  maxSelections?: number;\n  scaleMin?: number;\n  scaleMax?: number;\n  scaleLabels?: string[];\n}\n\nexport interface Section {\n  id: string;\n  title: string;\n  description: string;\n  questions: Question[];\n  userType: UserType;\n}\n\nexport interface AssessmentInput {\n  userType: UserType;\n  answers: Record<string, any>;\n}\n\nclass ApiClient {\n  private baseUrl: string;\n\n  constructor(baseUrl: string = API_BASE_URL) {\n    this.baseUrl = baseUrl;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    const url = `${this.baseUrl}${endpoint}`;\n\n    const defaultHeaders = {\n      \"Content-Type\": \"application/json\",\n    };\n\n    const config: RequestInit = {\n      ...options,\n      headers: {\n        ...defaultHeaders,\n        ...options.headers,\n      },\n    };\n\n    try {\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error(\"API request failed:\", error);\n      throw error;\n    }\n  }\n\n  async getCareerRecommendations(\n    input: CareerPathRecommendationsInput\n  ): Promise<ApiResponse<CareerPathRecommendationsOutput>> {\n    return this.request<CareerPathRecommendationsOutput>(\n      \"/career/recommendations\",\n      {\n        method: \"POST\",\n        body: JSON.stringify(input),\n      }\n    );\n  }\n\n  async getCareerInsights(\n    input: CareerInsightsInput\n  ): Promise<ApiResponse<CareerInsightsOutput>> {\n    return this.request<CareerInsightsOutput>(\"/career/insights\", {\n      method: \"POST\",\n      body: JSON.stringify(input),\n    });\n  }\n\n  async healthCheck(): Promise<ApiResponse> {\n    return this.request(\"/health\");\n  }\n\n  async getUserTypes(): Promise<ApiResponse<UserType[]>> {\n    return this.request<UserType[]>(\"/questions/user-types\");\n  }\n\n  async getQuestionSections(\n    userType: UserType\n  ): Promise<ApiResponse<Section[]>> {\n    return this.request<Section[]>(`/questions/sections?userType=${userType}`);\n  }\n\n  async getQuestionSection(\n    sectionId: string,\n    userType: UserType\n  ): Promise<ApiResponse<Section>> {\n    return this.request<Section>(\n      `/questions/section/${sectionId}?userType=${userType}`\n    );\n  }\n\n  async validateAnswers(input: AssessmentInput): Promise<ApiResponse<any>> {\n    return this.request<any>(\"/questions/validate\", {\n      method: \"POST\",\n      body: JSON.stringify(input),\n    });\n  }\n\n  async processAssessment(\n    input: AssessmentInput\n  ): Promise<ApiResponse<CareerPathRecommendationsOutput>> {\n    return this.request<CareerPathRecommendationsOutput>(\"/career/assessment\", {\n      method: \"POST\",\n      body: JSON.stringify(input),\n    });\n  }\n}\n\nexport const apiClient = new ApiClient();\n\n// Legacy function wrappers for compatibility\nexport async function getCareerRecommendationsAction(\n  input: CareerPathRecommendationsInput\n): Promise<{\n  success: boolean;\n  data?: CareerPathRecommendationsOutput;\n  error?: string;\n}> {\n  try {\n    const response = await apiClient.getCareerRecommendations(input);\n    return {\n      success: response.success,\n      data: response.data,\n      error: response.error,\n    };\n  } catch (error) {\n    const errorMessage =\n      error instanceof Error ? error.message : \"An unknown error occurred\";\n    return {\n      success: false,\n      error: `Failed to get career recommendations: ${errorMessage}`,\n    };\n  }\n}\n\nexport async function getCareerInsightsAction(\n  careerPath: string\n): Promise<{ success: boolean; data?: CareerInsightsOutput; error?: string }> {\n  try {\n    const response = await apiClient.getCareerInsights({ careerPath });\n    return {\n      success: response.success,\n      data: response.data,\n      error: response.error,\n    };\n  } catch (error) {\n    const errorMessage =\n      error instanceof Error ? error.message : \"An unknown error occurred\";\n    return {\n      success: false,\n      error: `Failed to get career insights: ${errorMessage}`,\n    };\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;AAGC;AADF,MAAM,eACJ,iEAAmC;AAgC9B,IAAA,AAAK,kCAAA;;;WAAA;;AAKL,IAAA,AAAK,sCAAA;;;;;;;WAAA;;AAkCZ,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,iBAAiB;YACrB,gBAAgB;QAClB;QAEA,MAAM,SAAsB;YAC1B,GAAG,OAAO;YACV,SAAS;gBACP,GAAG,cAAc;gBACjB,GAAG,QAAQ,OAAO;YACpB;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR;IACF;IAEA,MAAM,yBACJ,KAAqC,EACkB;QACvD,OAAO,IAAI,CAAC,OAAO,CACjB,2BACA;YACE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IAEJ;IAEA,MAAM,kBACJ,KAA0B,EACkB;QAC5C,OAAO,IAAI,CAAC,OAAO,CAAuB,oBAAoB;YAC5D,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAoC;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,MAAM,eAAiD;QACrD,OAAO,IAAI,CAAC,OAAO,CAAa;IAClC;IAEA,MAAM,oBACJ,QAAkB,EACe;QACjC,OAAO,IAAI,CAAC,OAAO,CAAY,CAAC,6BAA6B,EAAE,UAAU;IAC3E;IAEA,MAAM,mBACJ,SAAiB,EACjB,QAAkB,EACa;QAC/B,OAAO,IAAI,CAAC,OAAO,CACjB,CAAC,mBAAmB,EAAE,UAAU,UAAU,EAAE,UAAU;IAE1D;IAEA,MAAM,gBAAgB,KAAsB,EAA6B;QACvE,OAAO,IAAI,CAAC,OAAO,CAAM,uBAAuB;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,kBACJ,KAAsB,EACiC;QACvD,OAAO,IAAI,CAAC,OAAO,CAAkC,sBAAsB;YACzE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF;AAEO,MAAM,YAAY,IAAI;AAGtB,eAAe,+BACpB,KAAqC;IAMrC,IAAI;QACF,MAAM,WAAW,MAAM,UAAU,wBAAwB,CAAC;QAC1D,OAAO;YACL,SAAS,SAAS,OAAO;YACzB,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,KAAK;QACvB;IACF,EAAE,OAAO,OAAO;QACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YACL,SAAS;YACT,OAAO,CAAC,sCAAsC,EAAE,cAAc;QAChE;IACF;AACF;AAEO,eAAe,wBACpB,UAAkB;IAElB,IAAI;QACF,MAAM,WAAW,MAAM,UAAU,iBAAiB,CAAC;YAAE;QAAW;QAChE,OAAO;YACL,SAAS,SAAS,OAAO;YACzB,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,KAAK;QACvB;IACF,EAAE,OAAO,OAAO;QACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YACL,SAAS;YACT,OAAO,CAAC,+BAA+B,EAAE,cAAc;QACzD;IACF;AACF", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/context/AssessmentContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, {\n  createContext,\n  useContext,\n  useState,\n  useEffect,\n  ReactNode,\n  useCallback,\n} from \"react\";\nimport { Answers, UserType, Section } from \"@/lib/types\";\nimport type { CareerPathRecommendationsOutput } from \"@/lib/api\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { apiClient } from \"@/lib/api\";\n\ninterface AssessmentContextType {\n  userType: UserType | null;\n  setUserType: (userType: UserType) => void;\n  sections: Section[];\n  loadSections: (userType: UserType) => Promise<void>;\n  answers: Answers;\n  setAnswer: (id: string, value: any) => void;\n  results: CareerPathRecommendationsOutput | null;\n  setResults: (results: CareerPathRecommendationsOutput | null) => void;\n  isLoading: boolean;\n  error: string | null;\n  submitAssessment: () => Promise<void>;\n  clearProgress: () => void;\n  getInsights: (career: string) => Promise<any>;\n}\n\nconst AssessmentContext = createContext<AssessmentContextType | undefined>(\n  undefined\n);\n\nexport const AssessmentProvider = ({ children }: { children: ReactNode }) => {\n  const [userType, setUserType] = useState<UserType | null>(null);\n  const [sections, setSections] = useState<Section[]>([]);\n  const [answers, setAnswers] = useState<Answers>({});\n  const [results, setResults] =\n    useState<CareerPathRecommendationsOutput | null>(null);\n  const [isLoading, setIsLoading] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const { toast } = useToast();\n\n  useEffect(() => {\n    try {\n      const storedAnswers = localStorage.getItem(\"careerCompassAnswers\");\n      if (storedAnswers) {\n        setAnswers(JSON.parse(storedAnswers));\n      }\n      const storedResults = localStorage.getItem(\"careerCompassResults\");\n      if (storedResults) {\n        setResults(JSON.parse(storedResults));\n      }\n    } catch (e) {\n      console.error(\"Failed to load from localStorage\", e);\n      toast({\n        title: \"Error\",\n        description: \"Could not load your previous session.\",\n        variant: \"destructive\",\n      });\n    }\n  }, [toast]);\n\n  const loadSections = useCallback(\n    async (selectedUserType: UserType) => {\n      try {\n        setIsLoading(true);\n        const response = await apiClient.getQuestionSections(selectedUserType);\n        if (response.success && response.data) {\n          setSections(response.data);\n          setUserType(selectedUserType);\n        } else {\n          throw new Error(response.error || \"Failed to load questions\");\n        }\n      } catch (error) {\n        const errorMessage =\n          error instanceof Error ? error.message : \"Failed to load questions\";\n        setError(errorMessage);\n        toast({\n          title: \"Error\",\n          description: errorMessage,\n          variant: \"destructive\",\n        });\n      } finally {\n        setIsLoading(false);\n      }\n    },\n    [toast]\n  );\n\n  const setAnswer = useCallback(\n    (id: string, value: any) => {\n      const newAnswers = { ...answers, [id]: value };\n      setAnswers(newAnswers);\n      try {\n        localStorage.setItem(\n          \"careerCompassAnswers\",\n          JSON.stringify(newAnswers)\n        );\n      } catch (e) {\n        console.error(\"Failed to save answers to localStorage\", e);\n      }\n    },\n    [answers]\n  );\n\n  const mapSectionIdToInputKey = (sectionId: string): string => {\n    const map: { [key: string]: string } = {\n      interest_subject_preferences: \"interestAndSubjectPreferences\",\n      personality_learning_style: \"personalityAndLearningStyle\",\n      skills_strengths: \"skillsAndStrengths\",\n      career_aspirations_work_preferences:\n        \"careerAspirationsAndWorkPreferences\",\n      academic_background_performance: \"academicBackgroundAndPerformance\",\n    };\n    return map[sectionId] || sectionId;\n  };\n\n  const submitAssessment = useCallback(async () => {\n    if (!userType) {\n      setError(\"Please select a user type first\");\n      return;\n    }\n\n    // Prevent multiple submissions\n    if (isLoading) {\n      console.log(\"Assessment submission already in progress, skipping...\");\n      return;\n    }\n\n    console.log(\"Starting assessment submission...\");\n    setIsLoading(true);\n    setError(null);\n    try {\n      const assessmentInput = {\n        userType,\n        answers,\n      };\n\n      console.log(\"Sending assessment to backend:\", assessmentInput);\n      const response = await apiClient.processAssessment(assessmentInput);\n\n      if (response.success && response.data) {\n        setResults(response.data);\n        localStorage.setItem(\n          \"careerCompassResults\",\n          JSON.stringify(response.data)\n        );\n        toast({\n          title: \"Success!\",\n          description: \"Your career recommendations are ready.\",\n        });\n      } else {\n        throw new Error(response.error || \"An unknown error occurred.\");\n      }\n    } catch (e: any) {\n      setError(e.message || \"Failed to submit assessment.\");\n      toast({\n        title: \"Error\",\n        description: e.message || \"Failed to submit assessment.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  }, [userType, answers, isLoading, toast]);\n\n  const clearProgress = () => {\n    setAnswers({});\n    setResults(null);\n    try {\n      localStorage.removeItem(\"careerCompassAnswers\");\n      localStorage.removeItem(\"careerCompassResults\");\n      toast({\n        title: \"Progress Cleared\",\n        description: \"Your assessment has been reset.\",\n      });\n    } catch (e) {\n      console.error(\"Failed to clear localStorage\", e);\n    }\n  };\n\n  const getInsights = useCallback(\n    async (career: string) => {\n      setIsLoading(true);\n      try {\n        const response = await apiClient.getCareerInsights({\n          careerPath: career,\n        });\n        if (response.success && response.data) {\n          return response.data;\n        }\n        throw new Error(response.error || \"An unknown error occurred.\");\n      } catch (e: any) {\n        setError(e.message);\n        toast({\n          title: \"Error fetching insights\",\n          description: e.message,\n          variant: \"destructive\",\n        });\n        return null;\n      } finally {\n        setIsLoading(false);\n      }\n    },\n    [toast]\n  );\n\n  return (\n    <AssessmentContext.Provider\n      value={{\n        userType,\n        setUserType,\n        sections,\n        loadSections,\n        answers,\n        setAnswer,\n        results,\n        setResults,\n        isLoading,\n        error,\n        submitAssessment,\n        clearProgress,\n        getInsights,\n      }}\n    >\n      {children}\n    </AssessmentContext.Provider>\n  );\n};\n\nexport const useAssessment = () => {\n  const context = useContext(AssessmentContext);\n  if (context === undefined) {\n    throw new Error(\"useAssessment must be used within an AssessmentProvider\");\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAUA;AACA;;;AAbA;;;;AA+BA,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EACpC;AAGK,MAAM,qBAAqB,CAAC,EAAE,QAAQ,EAA2B;;IACtE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,CAAC;IACjD,MAAM,CAAC,SAAS,WAAW,GACzB,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0C;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD;IAEzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI;gBACF,MAAM,gBAAgB,aAAa,OAAO,CAAC;gBAC3C,IAAI,eAAe;oBACjB,WAAW,KAAK,KAAK,CAAC;gBACxB;gBACA,MAAM,gBAAgB,aAAa,OAAO,CAAC;gBAC3C,IAAI,eAAe;oBACjB,WAAW,KAAK,KAAK,CAAC;gBACxB;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;YACF;QACF;uCAAG;QAAC;KAAM;IAEV,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAC7B,OAAO;YACL,IAAI;gBACF,aAAa;gBACb,MAAM,WAAW,MAAM,gIAAA,CAAA,YAAS,CAAC,mBAAmB,CAAC;gBACrD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,YAAY,SAAS,IAAI;oBACzB,YAAY;gBACd,OAAO;oBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC3C,SAAS;gBACT,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;YACF,SAAU;gBACR,aAAa;YACf;QACF;uDACA;QAAC;KAAM;IAGT,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAC1B,CAAC,IAAY;YACX,MAAM,aAAa;gBAAE,GAAG,OAAO;gBAAE,CAAC,GAAG,EAAE;YAAM;YAC7C,WAAW;YACX,IAAI;gBACF,aAAa,OAAO,CAClB,wBACA,KAAK,SAAS,CAAC;YAEnB,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,0CAA0C;YAC1D;QACF;oDACA;QAAC;KAAQ;IAGX,MAAM,yBAAyB,CAAC;QAC9B,MAAM,MAAiC;YACrC,8BAA8B;YAC9B,4BAA4B;YAC5B,kBAAkB;YAClB,qCACE;YACF,iCAAiC;QACnC;QACA,OAAO,GAAG,CAAC,UAAU,IAAI;IAC3B;IAEA,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACnC,IAAI,CAAC,UAAU;gBACb,SAAS;gBACT;YACF;YAEA,+BAA+B;YAC/B,IAAI,WAAW;gBACb,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,aAAa;YACb,SAAS;YACT,IAAI;gBACF,MAAM,kBAAkB;oBACtB;oBACA;gBACF;gBAEA,QAAQ,GAAG,CAAC,kCAAkC;gBAC9C,MAAM,WAAW,MAAM,gIAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC;gBAEnD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,WAAW,SAAS,IAAI;oBACxB,aAAa,OAAO,CAClB,wBACA,KAAK,SAAS,CAAC,SAAS,IAAI;oBAE9B,MAAM;wBACJ,OAAO;wBACP,aAAa;oBACf;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;gBACpC;YACF,EAAE,OAAO,GAAQ;gBACf,SAAS,EAAE,OAAO,IAAI;gBACtB,MAAM;oBACJ,OAAO;oBACP,aAAa,EAAE,OAAO,IAAI;oBAC1B,SAAS;gBACX;YACF,SAAU;gBACR,aAAa;YACf;QACF;2DAAG;QAAC;QAAU;QAAS;QAAW;KAAM;IAExC,MAAM,gBAAgB;QACpB,WAAW,CAAC;QACZ,WAAW;QACX,IAAI;YACF,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF,EAAE,OAAO,GAAG;YACV,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAC5B,OAAO;YACL,aAAa;YACb,IAAI;gBACF,MAAM,WAAW,MAAM,gIAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC;oBACjD,YAAY;gBACd;gBACA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,OAAO,SAAS,IAAI;gBACtB;gBACA,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC,EAAE,OAAO,GAAQ;gBACf,SAAS,EAAE,OAAO;gBAClB,MAAM;oBACJ,OAAO;oBACP,aAAa,EAAE,OAAO;oBACtB,SAAS;gBACX;gBACA,OAAO;YACT,SAAU;gBACR,aAAa;YACf;QACF;sDACA;QAAC;KAAM;IAGT,qBACE,6LAAC,kBAAkB,QAAQ;QACzB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;GApMa;;QAQO,2IAAA,CAAA,WAAQ;;;KARf;AAsMN,MAAM,gBAAgB;;IAC3B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/app/providers.tsx"], "sourcesContent": ["'use client';\nimport { AssessmentProvider } from '@/context/AssessmentContext';\nimport { ThemeProvider as NextThemesProvider } from 'next-themes';\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  return (\n    <NextThemesProvider attribute=\"class\" defaultTheme=\"system\" enableSystem>\n      <AssessmentProvider>{children}</AssessmentProvider>\n    </NextThemesProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAIO,SAAS,UAAU,EAAE,QAAQ,EAAiC;IACnE,qBACE,6LAAC,mJAAA,CAAA,gBAAkB;QAAC,WAAU;QAAQ,cAAa;QAAS,YAAY;kBACtE,cAAA,6LAAC,mJAAA,CAAA,qBAAkB;sBAAE;;;;;;;;;;;AAG3B;KANgB", "debugId": null}}, {"offset": {"line": 823, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Compass } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from '@/components/ui/button';\n\nconst navLinks = [\n  { href: '/', label: 'Home' },\n  { href: '/assessment', label: 'Assessment' },\n  { href: '/resources', label: 'Resources' },\n];\n\nexport default function Header() {\n  const pathname = usePathname();\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container flex h-14 items-center\">\n        <div className=\"mr-4 flex items-center\">\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Compass className=\"h-6 w-6 text-primary\" />\n            <span className=\"font-bold font-headline sm:inline-block\">\n              Career Compass\n            </span>\n          </Link>\n        </div>\n        <nav className=\"hidden md:flex items-center space-x-6 text-sm font-medium\">\n          {navLinks.map((link) => (\n            <Link\n              key={link.href}\n              href={link.href}\n              className={cn(\n                'transition-colors hover:text-foreground/80',\n                pathname === link.href ? 'text-foreground' : 'text-foreground/60'\n              )}\n            >\n              {link.label}\n            </Link>\n          ))}\n        </nav>\n        <div className=\"flex flex-1 items-center justify-end\">\n            <Button asChild>\n                <Link href=\"/assessment\">Get Started</Link>\n            </Button>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,WAAW;IACf;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QAAE,MAAM;QAAe,OAAO;IAAa;IAC3C;QAAE,MAAM;QAAc,OAAO;IAAY;CAC1C;AAEc,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAK,WAAU;0CAA0C;;;;;;;;;;;;;;;;;8BAK9D,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAA,GAAA,kIAAA,CAAA,KAAE,AAAD,EACV,8CACA,aAAa,KAAK,IAAI,GAAG,oBAAoB;sCAG9C,KAAK,KAAK;2BAPN,KAAK,IAAI;;;;;;;;;;8BAWpB,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC,iJAAA,CAAA,SAAM;wBAAC,OAAO;kCACX,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;GApCwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}]}