{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/zenith/carear_assesment/frontend/src/app/page.tsx"], "sourcesContent": ["import { But<PERSON> } from '@/components/ui/button';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';\nimport { CheckCircle, BrainCircuit, LineChart, Library } from 'lucide-react';\nimport Link from 'next/link';\n\nconst features = [\n  {\n    icon: <CheckCircle className=\"h-8 w-8 text-primary\" />,\n    title: 'Dynamic Questionnaire',\n    description: 'Answer questions about your interests, personality, and skills to build a comprehensive profile.',\n  },\n  {\n    icon: <BrainCircuit className=\"h-8 w-8 text-primary\" />,\n    title: 'AI-Powered Recommendations',\n    description: 'Receive personalized career path suggestions based on our advanced AI analysis.',\n  },\n  {\n    icon: <LineChart className=\"h-8 w-8 text-primary\" />,\n    title: 'In-Depth Career Insights',\n    description: 'Explore future outlooks, potential challenges, and summaries for each recommended career.',\n  },\n  {\n    icon: <Library className=\"h-8 w-8 text-primary\" />,\n    title: 'Resource Library',\n    description: 'Access curated articles, videos, and courses to support your career exploration journey.',\n  },\n];\n\nexport default function Home() {\n  return (\n    <div className=\"flex flex-col items-center\">\n      <section className=\"w-full py-20 md:py-32 bg-card border-b\">\n        <div className=\"container px-4 md:px-6\">\n          <div className=\"grid gap-6 lg:grid-cols-2 lg:gap-12 items-center\">\n            <div className=\"space-y-4\">\n              <h1 className=\"text-4xl font-headline font-bold tracking-tighter sm:text-5xl md:text-6xl text-primary-foreground\">\n                Find Your Future with Career Compass\n              </h1>\n              <p className=\"max-w-[600px] text-muted-foreground md:text-xl\">\n                Our AI-powered assessment helps you discover career paths that match your unique personality, interests, and skills. Start your journey to a fulfilling career today.\n              </p>\n              <div className=\"flex flex-col gap-2 min-[400px]:flex-row\">\n                <Button asChild size=\"lg\">\n                  <Link href=\"/assessment\">Start Your Free Assessment</Link>\n                </Button>\n              </div>\n            </div>\n            <div className=\"flex justify-center\">\n                <svg\n                    className=\"w-full max-w-md\"\n                    viewBox=\"0 0 200 200\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    aria-label=\"An abstract illustration representing guidance and direction.\"\n                >\n                    <path\n                        fill=\"hsl(var(--primary))\"\n                        d=\"M48.4,-67.9C61,-57.1,68.4,-42,72.6,-26.2C76.8,-10.3,77.8,6.4,72.2,20.4C66.6,34.4,54.4,45.8,41.2,55.3C28,64.8,14,72.5,-1.2,74C-16.4,75.5,-32.8,70.8,-46.4,62.2C-60,53.6,-70.8,41.1,-75.9,26.7C-81,12.3,-80.4,-4,-74.8,-18.2C-69.2,-32.4,-58.6,-44.6,-46.5,-55.1C-34.4,-65.6,-20.8,-74.5,-5.5,-72.9C9.8,-71.4,19.6,-59.4,29.8,-52.3C40.1,-45.3,50.8,-43.3,48.4,-67.9\"\n                        transform=\"translate(100 100) scale(1.2)\"\n                    ></path>\n                </svg>\n            </div>\n          </div>\n        </div>\n      </section>\n      <section id=\"features\" className=\"w-full py-20 md:py-32\">\n        <div className=\"container px-4 md:px-6\">\n          <div className=\"flex flex-col items-center justify-center space-y-4 text-center\">\n            <div className=\"space-y-2\">\n              <div className=\"inline-block rounded-lg bg-secondary px-3 py-1 text-sm\">\n                Key Features\n              </div>\n              <h2 className=\"text-3xl font-headline font-bold tracking-tighter sm:text-5xl\">\n                Your Personal Career Guide\n              </h2>\n              <p className=\"max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed\">\n                We provide a comprehensive suite of tools to help you navigate the complex world of career choices with confidence and clarity.\n              </p>\n            </div>\n          </div>\n          <div className=\"mx-auto grid max-w-5xl items-start gap-8 sm:grid-cols-2 md:gap-12 lg:max-w-none lg:grid-cols-4 mt-12\">\n            {features.map((feature) => (\n              <Card key={feature.title} className=\"h-full\">\n                <CardHeader className=\"flex flex-col items-center text-center\">\n                  {feature.icon}\n                  <CardTitle className=\"mt-4 font-headline text-2xl\">{feature.title}</CardTitle>\n                </CardHeader>\n                <CardContent className=\"text-center text-muted-foreground\">\n                  {feature.description}\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;AAEA,MAAM,WAAW;IACf;QACE,oBAAM,8OAAC,2NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC7B,OAAO;QACP,aAAa;IACf;IACA;QACE,oBAAM,8OAAC,sNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;QAC9B,OAAO;QACP,aAAa;IACf;IACA;QACE,oBAAM,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,OAAO;QACP,aAAa;IACf;IACA;QACE,oBAAM,8OAAC,wMAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACzB,OAAO;QACP,aAAa;IACf;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoG;;;;;;kDAGlH,8OAAC;wCAAE,WAAU;kDAAiD;;;;;;kDAG9D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8IAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,MAAK;sDACnB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAc;;;;;;;;;;;;;;;;;;;;;;0CAI/B,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC;oCACG,WAAU;oCACV,SAAQ;oCACR,OAAM;oCACN,cAAW;8CAEX,cAAA,8OAAC;wCACG,MAAK;wCACL,GAAE;wCACF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5B,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAyD;;;;;;kDAGxE,8OAAC;wCAAG,WAAU;kDAAgE;;;;;;kDAG9E,8OAAC;wCAAE,WAAU;kDAAiG;;;;;;;;;;;;;;;;;sCAKlH,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,4IAAA,CAAA,OAAI;oCAAqB,WAAU;;sDAClC,8OAAC,4IAAA,CAAA,aAAU;4CAAC,WAAU;;gDACnB,QAAQ,IAAI;8DACb,8OAAC,4IAAA,CAAA,YAAS;oDAAC,WAAU;8DAA+B,QAAQ,KAAK;;;;;;;;;;;;sDAEnE,8OAAC,4IAAA,CAAA,cAAW;4CAAC,WAAU;sDACpB,QAAQ,WAAW;;;;;;;mCANb,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAetC", "debugId": null}}]}