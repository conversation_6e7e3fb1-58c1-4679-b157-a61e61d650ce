{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "noPropertyAccessFromIndexSignature": true, "noEmit": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "removeComments": false, "skipLibCheck": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/types/*": ["./src/types/*"], "@/config/*": ["./src/config/*"], "@/services/*": ["./src/services/*"], "@/routes/*": ["./src/routes/*"], "@/middleware/*": ["./src/middleware/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"]}