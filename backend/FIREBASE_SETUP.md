# Firebase Setup Guide

This guide will help you set up Firebase for the Career Compass backend and obtain all required environment variables.

## Prerequisites

- A Google account
- Access to the [Firebase Console](https://console.firebase.google.com/)

## Step 1: Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter your project name (e.g., "career-compass")
4. Choose whether to enable Google Analytics (optional)
5. Click "Create project"

## Step 2: Enable Required Services

### Enable Authentication
1. In your Firebase project, go to **Authentication** in the left sidebar
2. Click "Get started"
3. Go to the **Sign-in method** tab
4. Enable the sign-in providers you want to use:
   - Email/Password (recommended)
   - Google (optional)
   - Other providers as needed

### Enable Firestore Database
1. Go to **Firestore Database** in the left sidebar
2. Click "Create database"
3. <PERSON>ose "Start in test mode" (you can change security rules later)
4. Select a location for your database (choose closest to your users)
5. Click "Done"

## Step 3: Get Firebase Admin SDK Credentials

1. In your Firebase project, click the **gear icon** ⚙️ next to "Project Overview"
2. Select **Project settings**
3. Go to the **Service accounts** tab
4. Click **Generate new private key**
5. Click **Generate key** - this will download a JSON file

## Step 4: Extract Environment Variables

From the downloaded JSON file, extract these values for your `.env` file:

```json
{
  "type": "service_account",
  "project_id": "your-project-id",           // → FIREBASE_PROJECT_ID
  "private_key_id": "...",
  "private_key": "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n",  // → FIREBASE_PRIVATE_KEY
  "client_email": "<EMAIL>",  // → FIREBASE_CLIENT_EMAIL
  "client_id": "...",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token",
  "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
  "client_x509_cert_url": "..."
}
```

## Step 5: Set Up Environment Variables

Create a `.env` file in the backend directory and add:

```bash
# Firebase Configuration
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key_content_here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Google AI Configuration
GOOGLE_GENAI_API_KEY=your_google_ai_api_key
```

## Step 6: Get Google AI API Key

1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Sign in with your Google account
3. Click "Get API key"
4. Create a new API key or use an existing one
5. Copy the API key and add it to your `.env` file as `GOOGLE_GENAI_API_KEY`

## Step 7: Configure Firestore Security Rules (Optional)

For production, update your Firestore security rules:

1. Go to **Firestore Database** > **Rules**
2. Replace the default rules with appropriate security rules for your app

Example rules:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Assessments and results are user-specific
    match /assessments/{document} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    match /results/{document} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
  }
}
```

## Important Security Notes

1. **Never commit your `.env` file** to version control
2. **Keep your service account key secure** - it provides admin access to your Firebase project
3. **Use different Firebase projects** for development, staging, and production
4. **Regularly rotate your API keys** and service account keys
5. **Set up proper Firestore security rules** before going to production

## Testing Your Setup

After setting up your environment variables, you can test your Firebase connection by running:

```bash
cd backend
bun run dev
```

Check the console for "✅ Firebase Admin SDK initialized successfully" message.

## Troubleshooting

### Common Issues

1. **"Firebase credentials are missing"**
   - Ensure all required environment variables are set
   - Check that your `.env` file is in the correct location

2. **"Invalid private key"**
   - Make sure the private key includes the full content with `-----BEGIN PRIVATE KEY-----` and `-----END PRIVATE KEY-----`
   - Ensure proper escaping of newlines in the private key

3. **"Project not found"**
   - Verify the project ID matches exactly with your Firebase project
   - Ensure the service account has the correct permissions

4. **"Permission denied"**
   - Check that your service account has the necessary roles (Firebase Admin SDK Admin Service Agent)
   - Verify Firestore security rules allow the operations you're trying to perform
