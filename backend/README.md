# Career Compass Backend

A Bun + Express backend API for the Career Compass application, providing AI-powered career assessment and recommendation services.

## Features

- 🤖 **AI-Powered Career Recommendations** - Using Google's Gemini AI via Genkit
- 🔥 **Firebase Integration** - Authentication and Firestore database
- 🚀 **High Performance** - Built with Bun runtime for optimal speed
- 🛡️ **Security** - Rate limiting, CORS, helmet, and authentication middleware
- 📊 **Comprehensive Logging** - Request logging and error tracking
- 🔧 **Type Safety** - Full TypeScript support with strict configuration

## Tech Stack

- **Runtime**: Bun
- **Framework**: Express.js
- **AI**: Google Genkit with Gemini 2.0 Flash
- **Database**: Firebase Firestore
- **Authentication**: Firebase Auth
- **Language**: TypeScript
- **Validation**: Zod

## Getting Started

### Prerequisites

- [Bun](https://bun.sh/) >= 1.0.0
- Firebase project with Firestore and Authentication enabled
- Google AI API key

### Installation

1. Clone the repository and navigate to the backend directory:
```bash
cd backend
```

2. Install dependencies:
```bash
bun install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

4. Configure your `.env` file with your Firebase and Google AI credentials.

### Development

Start the development server with hot reload:
```bash
bun run dev
```

The server will start on `http://localhost:3001` by default.

### Production

Build and start the production server:
```bash
bun run build
bun run start
```

## API Endpoints

### Health Check
- `GET /api/health` - Basic health check
- `GET /api/health/detailed` - Detailed system information

### Career Services
- `POST /api/career/recommendations` - Generate career recommendations
- `POST /api/career/insights` - Get insights for a specific career path

## Environment Variables

See `.env.example` for all required environment variables.

## Project Structure

```
src/
├── config/          # Configuration files
├── middleware/      # Express middleware
├── routes/          # API route handlers
├── services/        # Business logic services
├── types/           # TypeScript type definitions
└── index.ts         # Application entry point
```

## Contributing

1. Follow TypeScript best practices
2. Use proper error handling
3. Add appropriate logging
4. Validate all inputs with Zod schemas
5. Write tests for new features
