{"name": "career-compass-backend", "version": "1.0.0", "description": "Backend API for Career Compass - AI-powered career assessment platform", "main": "src/index.ts", "scripts": {"dev": "bun --watch src/index.ts", "start": "bun src/index.ts", "build": "bun build src/index.ts --outdir ./dist --target=bun", "test": "bun test", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit"}, "dependencies": {"@genkit-ai/googleai": "^1.13.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.0", "express-rate-limit": "^7.4.1", "firebase-admin": "^13.0.1", "genkit": "^1.13.0", "helmet": "^8.0.0", "morgan": "^1.10.0", "zod": "^3.24.2"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/morgan": "^1.9.9", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "concurrently": "^9.2.0", "eslint": "^9.0.0", "typescript": "^5.0.0"}, "keywords": ["career", "assessment", "ai", "backend", "express", "bun", "firebase"], "author": "Career Compass Team", "license": "MIT", "engines": {"bun": ">=1.0.0"}}