import { Router, Request, Response } from "express";
import { getCareerRecommendations } from "@/services/careerRecommendations";
import { getCareerInsights } from "@/services/careerInsights";
import { questionsService } from "@/services/questionsService";
import { validateBody } from "@/middleware/validation";
import { asyncHand<PERSON> } from "@/middleware/errorHandler";
import { optionalAuth } from "@/middleware/auth";
import {
  CareerPathRecommendationsInputSchema,
  CareerInsightsInputSchema,
  ApiResponse,
  CareerPathRecommendationsOutput,
  CareerInsightsOutput,
} from "@/types/career";
import { AssessmentInputSchema } from "@/types/questions";

const router = Router();

// POST /api/career/recommendations
router.post(
  "/recommendations",
  optionalAuth,
  validateBody(CareerPathRecommendationsInputSchema),
  asyncHandler(async (req: Request, res: Response) => {
    const input = req.body;

    console.log("📝 Received career recommendations request");

    const result = await getCareerRecommendations(input);

    const response: ApiResponse<CareerPathRecommendationsOutput> = {
      success: true,
      data: result,
      message: "Career recommendations generated successfully",
    };

    res.json(response);
  })
);

// POST /api/career/insights
router.post(
  "/insights",
  optionalAuth,
  validateBody(CareerInsightsInputSchema),
  asyncHandler(async (req: Request, res: Response) => {
    const input = req.body;

    console.log(`📝 Received career insights request for: ${input.careerPath}`);

    const result = await getCareerInsights(input);

    const response: ApiResponse<CareerInsightsOutput> = {
      success: true,
      data: result,
      message: "Career insights generated successfully",
    };

    res.json(response);
  })
);

// POST /api/career/assessment
router.post(
  "/assessment",
  optionalAuth,
  validateBody(AssessmentInputSchema),
  asyncHandler(async (req: Request, res: Response) => {
    const { userType, answers } = req.body;

    console.log(`📝 Processing full assessment for user type: ${userType}`);

    // Validate answers
    const validation = questionsService.validateAnswers(answers, userType);

    if (!validation.isValid) {
      res.status(400).json({
        success: false,
        error: "Invalid answers provided",
        data: validation,
      });
      return;
    }

    // Transform answers for AI
    const transformedAnswers = questionsService.transformAnswersForAI(
      answers,
      userType
    );

    // Add user type to the input
    const aiInput = {
      userType,
      ...transformedAnswers,
    } as any; // Type assertion for now - the questionsService ensures all required fields are present

    // Get career recommendations
    const result = await getCareerRecommendations(aiInput);

    const response: ApiResponse<CareerPathRecommendationsOutput> = {
      success: true,
      data: result,
      message:
        "Assessment processed and career recommendations generated successfully",
    };

    res.json(response);
  })
);

export default router;
