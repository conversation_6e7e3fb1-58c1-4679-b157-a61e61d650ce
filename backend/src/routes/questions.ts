import { Router, Request, Response } from "express";
import { questionsService } from "@/services/questionsService";
import { validateQuery, validateBody } from "@/middleware/validation";
import { asyncHandler } from "@/middleware/errorHandler";
import { optionalAuth } from "@/middleware/auth";
import {
  UserType,
  QuestionResponseSchema,
  AssessmentInputSchema,
} from "@/types/questions";
import { ApiResponse } from "@/types/career";
import { z } from "zod";

const router = Router();

// Schema for user type query parameter
const UserTypeQuerySchema = z.object({
  userType: z.nativeEnum(UserType),
});

// GET /api/questions/user-types
router.get(
  "/user-types",
  asyncHandler(async (req: Request, res: Response) => {
    console.log("📝 Fetching available user types");

    const userTypes = questionsService.getAvailableUserTypes();

    const response: ApiResponse<UserType[]> = {
      success: true,
      data: userTypes,
      message: "User types retrieved successfully",
    };

    res.json(response);
  })
);

// GET /api/questions/sections?userType=student|professional
router.get(
  "/sections",
  validateQuery(UserTypeQuerySchema),
  asyncHandler(async (req: Request, res: Response) => {
    const { userType } = req.query as { userType: UserType };

    console.log(`📝 Fetching question sections for user type: ${userType}`);

    const sections = questionsService.getSectionsByUserType(userType);

    const response: ApiResponse<typeof sections> = {
      success: true,
      data: sections,
      message: `Question sections for ${userType} retrieved successfully`,
    };

    res.json(response);
  })
);

// GET /api/questions/section/:sectionId?userType=student|professional
router.get(
  "/section/:sectionId",
  validateQuery(UserTypeQuerySchema),
  asyncHandler(async (req: Request, res: Response) => {
    const { sectionId } = req.params;
    const { userType } = req.query as { userType: UserType };

    if (!sectionId) {
      res.status(400).json({
        success: false,
        error: "Section ID is required",
      });
      return;
    }

    console.log(`📝 Fetching section ${sectionId} for user type: ${userType}`);

    const section = questionsService.getSectionById(sectionId, userType);

    if (!section) {
      res.status(404).json({
        success: false,
        error: `Section ${sectionId} not found for user type ${userType}`,
      });
      return;
    }

    const response: ApiResponse<typeof section> = {
      success: true,
      data: section,
      message: `Section ${sectionId} retrieved successfully`,
    };

    res.json(response);
  })
);

// GET /api/questions/stats?userType=student|professional
router.get(
  "/stats",
  validateQuery(UserTypeQuerySchema),
  asyncHandler(async (req: Request, res: Response) => {
    const { userType } = req.query as { userType: UserType };

    console.log(`📝 Fetching question statistics for user type: ${userType}`);

    const stats = questionsService.getQuestionStats(userType);

    const response: ApiResponse<typeof stats> = {
      success: true,
      data: stats,
      message: `Question statistics for ${userType} retrieved successfully`,
    };

    res.json(response);
  })
);

// POST /api/questions/validate
router.post(
  "/validate",
  optionalAuth,
  validateBody(AssessmentInputSchema),
  asyncHandler(async (req: Request, res: Response) => {
    const { userType, answers } = req.body;

    console.log(`📝 Validating answers for user type: ${userType}`);

    const validation = questionsService.validateAnswers(answers, userType);

    const response: ApiResponse<typeof validation> = {
      success: validation.isValid,
      data: validation,
      message: validation.isValid
        ? "Answers validation successful"
        : "Answers validation failed",
    };

    if (!validation.isValid) {
      res.status(400).json(response);
      return;
    }

    res.json(response);
  })
);

// POST /api/questions/transform
router.post(
  "/transform",
  optionalAuth,
  validateBody(AssessmentInputSchema),
  asyncHandler(async (req: Request, res: Response) => {
    const { userType, answers } = req.body;

    console.log(`📝 Transforming answers for AI processing: ${userType}`);

    // First validate the answers
    const validation = questionsService.validateAnswers(answers, userType);

    if (!validation.isValid) {
      res.status(400).json({
        success: false,
        error: "Invalid answers provided",
        data: validation,
      });
      return;
    }

    // Transform answers for AI
    const transformedAnswers = questionsService.transformAnswersForAI(
      answers,
      userType
    );

    const response: ApiResponse<typeof transformedAnswers> = {
      success: true,
      data: transformedAnswers,
      message: "Answers transformed successfully for AI processing",
    };

    res.json(response);
  })
);

export default router;
