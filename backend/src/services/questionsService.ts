/**
 * @fileOverview Questions service for Career Compass
 * Manages question sets for different user types and assessment flows
 */

import { Section, UserType } from "@/types/questions";
import { studentSections } from "@/data/studentQuestions";
import { professionalSections } from "@/data/professionalQuestions";

export class QuestionsService {
  private static instance: QuestionsService;
  private studentQuestions: Section[];
  private professionalQuestions: Section[];

  private constructor() {
    this.studentQuestions = studentSections;
    this.professionalQuestions = professionalSections;
  }

  public static getInstance(): QuestionsService {
    if (!QuestionsService.instance) {
      QuestionsService.instance = new QuestionsService();
    }
    return QuestionsService.instance;
  }

  /**
   * Get all sections for a specific user type
   */
  public getSectionsByUserType(userType: UserType): Section[] {
    switch (userType) {
      case UserType.STUDENT:
        return this.studentQuestions;
      case UserType.PROFESSIONAL:
        return this.professionalQuestions;
      default:
        throw new Error(`Invalid user type: ${userType}`);
    }
  }

  /**
   * Get a specific section by ID and user type
   */
  public getSectionById(sectionId: string, userType: UserType): Section | null {
    const sections = this.getSectionsByUserType(userType);
    return sections.find((section) => section.id === sectionId) || null;
  }

  /**
   * Get all available user types
   */
  public getAvailableUserTypes(): UserType[] {
    return Object.values(UserType);
  }

  /**
   * Validate if answers are complete for a user type
   */
  public validateAnswers(
    answers: Record<string, any>,
    userType: UserType
  ): {
    isValid: boolean;
    missingQuestions: string[];
    errors: string[];
  } {
    const sections = this.getSectionsByUserType(userType);
    const missingQuestions: string[] = [];
    const errors: string[] = [];
    let totalQuestions = 0;

    for (const section of sections) {
      for (const question of section.questions) {
        totalQuestions++;

        // Check if answer exists and is not empty
        const answer = answers[question.id];
        const isEmpty =
          answer === undefined ||
          answer === null ||
          answer === "" ||
          (Array.isArray(answer) && answer.length === 0);

        if (question.required && isEmpty) {
          missingQuestions.push(question.id);
        }

        // Validate checkbox max selections
        if (
          question.type === "checkbox" &&
          question.maxSelections &&
          answers[question.id]
        ) {
          const selections = Array.isArray(answers[question.id])
            ? answers[question.id]
            : [];
          if (selections.length > question.maxSelections) {
            errors.push(
              `Question ${question.id} exceeds maximum selections (${question.maxSelections})`
            );
          }
        }

        // Validate scale ranges
        if (question.type === "scale" && answers[question.id] !== undefined) {
          const value = Number(answers[question.id]);
          if (question.scaleMin !== undefined && value < question.scaleMin) {
            errors.push(
              `Question ${question.id} value below minimum (${question.scaleMin})`
            );
          }
          if (question.scaleMax !== undefined && value > question.scaleMax) {
            errors.push(
              `Question ${question.id} value above maximum (${question.scaleMax})`
            );
          }
        }
      }
    }

    // Allow some missing questions for partial assessments
    // Calculate 70% of total questions as the maximum allowed missing
    const maxMissingAllowed = Math.floor(totalQuestions * 0.3); // Allow up to 30% missing (70% completion)

    console.log(
      `📊 Validation stats: Total questions: ${totalQuestions}, Missing: ${missingQuestions.length}, Max allowed missing: ${maxMissingAllowed}, Errors: ${errors.length}`
    );

    return {
      isValid:
        missingQuestions.length <= maxMissingAllowed && errors.length === 0,
      missingQuestions,
      errors,
    };
  }

  /**
   * Transform answers into structured format for AI processing
   */
  public transformAnswersForAI(
    answers: Record<string, any>,
    userType: UserType
  ): Record<string, string> {
    const sections = this.getSectionsByUserType(userType);
    const transformedAnswers: Record<string, string> = {};

    for (const section of sections) {
      const sectionAnswers: string[] = [];

      for (const question of section.questions) {
        const answer = answers[question.id];
        if (answer !== undefined && answer !== null) {
          let formattedAnswer = "";

          if (Array.isArray(answer)) {
            formattedAnswer = `${question.text}: ${answer.join(", ")}`;
          } else if (typeof answer === "object") {
            formattedAnswer = `${question.text}: ${JSON.stringify(answer)}`;
          } else {
            formattedAnswer = `${question.text}: ${answer}`;
          }

          sectionAnswers.push(formattedAnswer);
        }
      }

      // Map section IDs to the expected AI input format
      const sectionKey = this.mapSectionIdToInputKey(section.id);
      transformedAnswers[sectionKey] = sectionAnswers.join("\n");
    }

    // Ensure all required AI schema fields are present
    const requiredFields = [
      "interestAndSubjectPreferences",
      "personalityAndLearningStyle",
      "skillsAndStrengths",
      "careerAspirationsAndWorkPreferences",
      "academicBackgroundAndPerformance",
    ];

    for (const field of requiredFields) {
      if (!transformedAnswers[field]) {
        transformedAnswers[field] = "";
      }
    }

    return transformedAnswers;
  }

  /**
   * Map section IDs to AI input keys
   */
  private mapSectionIdToInputKey(sectionId: string): string {
    const mapping: Record<string, string> = {
      interest_subject_preferences: "interestAndSubjectPreferences",
      interest_passion_analysis: "interestAndSubjectPreferences",
      personality_learning_style: "personalityAndLearningStyle",
      personality_work_style: "personalityAndLearningStyle",
      skills_strengths: "skillsAndStrengths",
      career_aspirations_work_preferences:
        "careerAspirationsAndWorkPreferences",
      workplace_lifestyle_preferences: "careerAspirationsAndWorkPreferences",
      academic_background_performance: "academicBackgroundAndPerformance",
      education_career_background: "academicBackgroundAndPerformance",
    };

    return mapping[sectionId] || sectionId;
  }

  /**
   * Get question statistics
   */
  public getQuestionStats(userType: UserType): {
    totalSections: number;
    totalQuestions: number;
    requiredQuestions: number;
    questionsByType: Record<string, number>;
  } {
    const sections = this.getSectionsByUserType(userType);
    let totalQuestions = 0;
    let requiredQuestions = 0;
    const questionsByType: Record<string, number> = {};

    for (const section of sections) {
      totalQuestions += section.questions.length;

      for (const question of section.questions) {
        if (question.required) {
          requiredQuestions++;
        }

        questionsByType[question.type] =
          (questionsByType[question.type] || 0) + 1;
      }
    }

    return {
      totalSections: sections.length,
      totalQuestions,
      requiredQuestions,
      questionsByType,
    };
  }
}

// Export singleton instance
export const questionsService = QuestionsService.getInstance();
