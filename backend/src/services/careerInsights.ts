/**
 * @fileOverview Provides AI-powered insights into potential career paths,
 * including explanations, future outlooks, and potential challenges.
 */

import { ai } from '@/config/genkit';
import {
  CareerInsightsInput,
  CareerInsightsOutput,
  CareerInsightsInputSchema,
  CareerInsightsOutputSchema,
} from '@/types/career';

const careerInsightsPrompt = ai.definePrompt({
  name: 'careerInsightsPrompt',
  input: { schema: CareerInsightsInputSchema },
  output: { schema: CareerInsightsOutputSchema },
  prompt: `Provide insights into the career path: {{{careerPath}}}.  Include a summary, the future outlook, and potential challenges.  Format the output according to the schema.`,
});

const careerInsightsFlow = ai.defineFlow(
  {
    name: 'careerInsightsFlow',
    inputSchema: CareerInsightsInputSchema,
    outputSchema: CareerInsightsOutputSchema,
  },
  async (input) => {
    const { output } = await careerInsightsPrompt(input);
    return output!;
  }
);

export async function getCareerInsights(
  input: CareerInsightsInput
): Promise<CareerInsightsOutput> {
  try {
    console.log(`🤖 Generating career insights for: ${input.careerPath}`);
    const result = await careerInsightsFlow(input);
    console.log('✅ Career insights generated successfully');
    return result;
  } catch (error) {
    console.error('❌ Error generating career insights:', error);
    throw new Error('Failed to generate career insights');
  }
}
