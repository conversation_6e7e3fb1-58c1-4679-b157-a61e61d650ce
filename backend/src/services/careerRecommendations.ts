/**
 * @fileOverview A career path recommendation AI service.
 * Provides AI-powered career path recommendations based on student assessment data.
 */

import { ai } from "@/config/genkit";
import {
  CareerPathRecommendationsInput,
  CareerPathRecommendationsOutput,
  CareerPathRecommendationsInputSchema,
  CareerPathRecommendationsOutputSchema,
} from "@/types/career";

const careerPathRecommendationsPrompt = ai.definePrompt({
  name: "careerPathRecommendationsPrompt",
  input: { schema: CareerPathRecommendationsInputSchema },
  output: { schema: CareerPathRecommendationsOutputSchema },
  prompt: `You are a career counselor who provides personalized career path recommendations based on an individual's interests, personality, skills, aspirations, and background.

  User Type: {{{userType}}}

  Consider the following information provided:

  Interests and Subject Preferences: {{{interestAndSubjectPreferences}}}
  Personality and Learning Style: {{{personalityAndLearningStyle}}}
  Skills and Strengths: {{{skillsAndStrengths}}}
  Career Aspirations and Work Preferences: {{{careerAspirationsAndWorkPreferences}}}
  Academic/Professional Background: {{{academicBackgroundAndPerformance}}}

  Based on this information and the user type (student or professional), provide appropriate career recommendations and explain your reasoning.
  For students, focus on educational paths and entry-level career opportunities.
  For professionals, focus on career transitions, advancement opportunities, and skill development.

  Format the response as a JSON object with "careerRecommendations" (an array of career recommendations) and "reasoning" (the reasoning behind the recommendations) fields.
  `,
});

const careerPathRecommendationsFlow = ai.defineFlow(
  {
    name: "careerPathRecommendationsFlow",
    inputSchema: CareerPathRecommendationsInputSchema,
    outputSchema: CareerPathRecommendationsOutputSchema,
  },
  async (input) => {
    const { output } = await careerPathRecommendationsPrompt(input);
    return output!;
  }
);

export async function getCareerRecommendations(
  input: CareerPathRecommendationsInput
): Promise<CareerPathRecommendationsOutput> {
  try {
    console.log("🤖 Generating career recommendations for user input");
    const result = await careerPathRecommendationsFlow(input);
    console.log("✅ Career recommendations generated successfully");
    return result;
  } catch (error) {
    console.error("❌ Error generating career recommendations:", error);
    throw new Error("Failed to generate career recommendations");
  }
}
