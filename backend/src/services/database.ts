/**
 * @fileOverview Database service for Career Compass
 * Provides Firestore database operations for user data, assessments, and results
 */

import { getFirestore } from '@/config/firebase';
import { CareerPathRecommendationsOutput, CareerInsightsOutput } from '@/types/career';

const db = getFirestore();

// Collections
const USERS_COLLECTION = 'users';
const ASSESSMENTS_COLLECTION = 'assessments';
const RESULTS_COLLECTION = 'results';

export interface UserProfile {
  uid: string;
  email?: string;
  displayName?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface AssessmentData {
  id?: string;
  userId: string;
  answers: Record<string, any>;
  completedAt: Date;
  createdAt: Date;
}

export interface AssessmentResult {
  id?: string;
  userId: string;
  assessmentId: string;
  recommendations: CareerPathRecommendationsOutput;
  insights?: Record<string, CareerInsightsOutput>;
  createdAt: Date;
}

// User operations
export async function createUserProfile(userData: Omit<UserProfile, 'createdAt' | 'updatedAt'>): Promise<void> {
  const now = new Date();
  const userDoc = {
    ...userData,
    createdAt: now,
    updatedAt: now,
  };
  
  await db.collection(USERS_COLLECTION).doc(userData.uid).set(userDoc);
  console.log(`✅ User profile created for ${userData.uid}`);
}

export async function getUserProfile(uid: string): Promise<UserProfile | null> {
  const doc = await db.collection(USERS_COLLECTION).doc(uid).get();
  
  if (!doc.exists) {
    return null;
  }
  
  return doc.data() as UserProfile;
}

export async function updateUserProfile(uid: string, updates: Partial<UserProfile>): Promise<void> {
  const updateData = {
    ...updates,
    updatedAt: new Date(),
  };
  
  await db.collection(USERS_COLLECTION).doc(uid).update(updateData);
  console.log(`✅ User profile updated for ${uid}`);
}

// Assessment operations
export async function saveAssessment(assessmentData: Omit<AssessmentData, 'id' | 'createdAt'>): Promise<string> {
  const now = new Date();
  const assessment = {
    ...assessmentData,
    createdAt: now,
  };
  
  const docRef = await db.collection(ASSESSMENTS_COLLECTION).add(assessment);
  console.log(`✅ Assessment saved with ID: ${docRef.id}`);
  return docRef.id;
}

export async function getAssessment(assessmentId: string): Promise<AssessmentData | null> {
  const doc = await db.collection(ASSESSMENTS_COLLECTION).doc(assessmentId).get();
  
  if (!doc.exists) {
    return null;
  }
  
  return { id: doc.id, ...doc.data() } as AssessmentData;
}

export async function getUserAssessments(userId: string): Promise<AssessmentData[]> {
  const snapshot = await db
    .collection(ASSESSMENTS_COLLECTION)
    .where('userId', '==', userId)
    .orderBy('createdAt', 'desc')
    .get();
  
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as AssessmentData));
}

// Results operations
export async function saveAssessmentResult(resultData: Omit<AssessmentResult, 'id' | 'createdAt'>): Promise<string> {
  const now = new Date();
  const result = {
    ...resultData,
    createdAt: now,
  };
  
  const docRef = await db.collection(RESULTS_COLLECTION).add(result);
  console.log(`✅ Assessment result saved with ID: ${docRef.id}`);
  return docRef.id;
}

export async function getAssessmentResult(resultId: string): Promise<AssessmentResult | null> {
  const doc = await db.collection(RESULTS_COLLECTION).doc(resultId).get();
  
  if (!doc.exists) {
    return null;
  }
  
  return { id: doc.id, ...doc.data() } as AssessmentResult;
}

export async function getUserResults(userId: string): Promise<AssessmentResult[]> {
  const snapshot = await db
    .collection(RESULTS_COLLECTION)
    .where('userId', '==', userId)
    .orderBy('createdAt', 'desc')
    .get();
  
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as AssessmentResult));
}
