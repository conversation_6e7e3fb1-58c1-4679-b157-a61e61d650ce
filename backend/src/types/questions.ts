import { z } from 'zod';

// User Types
export enum UserType {
  STUDENT = 'student',
  PROFESSIONAL = 'professional'
}

// Question Types
export enum QuestionType {
  MCQ = 'mcq',
  CHECKBOX = 'checkbox',
  SHORT_ANSWER = 'short-answer',
  SCALE = 'scale',
  YES_NO = 'yes-no',
  RANKING = 'ranking'
}

// Question Interface
export interface Question {
  id: string;
  type: QuestionType;
  text: string;
  options?: string[];
  required?: boolean;
  maxSelections?: number; // For checkbox questions
  scaleMin?: number; // For scale questions
  scaleMax?: number; // For scale questions
  scaleLabels?: string[]; // For scale questions
}

// Section Interface
export interface Section {
  id: string;
  title: string;
  description: string;
  questions: Question[];
  userType: UserType;
}

// Assessment Input Schema
export const AssessmentInputSchema = z.object({
  userType: z.nativeEnum(UserType),
  answers: z.record(z.string(), z.any()),
});

export type AssessmentInput = z.infer<typeof AssessmentInputSchema>;

// Question Response Schema
export const QuestionResponseSchema = z.object({
  sections: z.array(z.object({
    id: z.string(),
    title: z.string(),
    description: z.string(),
    questions: z.array(z.object({
      id: z.string(),
      type: z.nativeEnum(QuestionType),
      text: z.string(),
      options: z.array(z.string()).optional(),
      required: z.boolean().optional(),
      maxSelections: z.number().optional(),
      scaleMin: z.number().optional(),
      scaleMax: z.number().optional(),
      scaleLabels: z.array(z.string()).optional(),
    })),
  })),
});

export type QuestionResponse = z.infer<typeof QuestionResponseSchema>;
