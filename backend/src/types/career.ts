import { z } from "zod";
import { UserType } from "./questions";

// Career Path Recommendations Types
export const CareerPathRecommendationsInputSchema = z.object({
  userType: z.nativeEnum(UserType),
  interestAndSubjectPreferences: z
    .string()
    .describe("Answers to questions about interest and subject preferences."),
  personalityAndLearningStyle: z
    .string()
    .describe("Answers to questions about personality and learning style."),
  skillsAndStrengths: z
    .string()
    .describe("Answers to questions about skills and strengths."),
  careerAspirationsAndWorkPreferences: z
    .string()
    .describe(
      "Answers to questions about career aspirations and work preferences."
    ),
  academicBackgroundAndPerformance: z
    .string()
    .describe(
      "Answers to questions about academic background and performance."
    ),
});

export const CareerPathRecommendationsOutputSchema = z.object({
  careerRecommendations: z
    .array(z.string())
    .describe(
      "A list of career recommendations based on the provided information."
    ),
  reasoning: z
    .string()
    .describe("The reasoning behind the career recommendations."),
});

export type CareerPathRecommendationsInput = z.infer<
  typeof CareerPathRecommendationsInputSchema
>;
export type CareerPathRecommendationsOutput = z.infer<
  typeof CareerPathRecommendationsOutputSchema
>;

// Career Insights Types
export const CareerInsightsInputSchema = z.object({
  careerPath: z
    .string()
    .describe("The name of the career path to get insights for."),
});

export const CareerInsightsOutputSchema = z.object({
  summary: z.string().describe("A summary of the career path."),
  futureOutlook: z.string().describe("The future outlook for the career path."),
  potentialChallenges: z
    .string()
    .describe("Potential challenges in the career path."),
});

export type CareerInsightsInput = z.infer<typeof CareerInsightsInputSchema>;
export type CareerInsightsOutput = z.infer<typeof CareerInsightsOutputSchema>;

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ApiError {
  message: string;
  code?: string;
  statusCode?: number;
}
