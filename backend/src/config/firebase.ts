import admin from "firebase-admin";
import { env } from "./env";

let firebaseApp: admin.app.App;

export function initializeFirebase(): admin.app.App | null {
  if (firebaseApp) {
    return firebaseApp;
  }

  try {
    // Check if we're in development with placeholder credentials
    if (
      env.FIREBASE_PROJECT_ID.includes("demo-project") ||
      env.FIREBASE_PRIVATE_KEY.includes("placeholder") ||
      env.FIREBASE_CLIENT_EMAIL.includes("demo-project")
    ) {
      console.log(
        "⚠️ Firebase Admin SDK skipped - using placeholder credentials"
      );
      console.log(
        "   To enable Firebase, update your .env file with real credentials"
      );
      return null;
    }

    // Initialize Firebase Admin SDK
    if (!env.FIREBASE_PRIVATE_KEY || !env.FIREBASE_CLIENT_EMAIL) {
      throw new Error("Firebase credentials are missing");
    }

    const serviceAccount: admin.ServiceAccount = {
      projectId: env.FIREBASE_PROJECT_ID,
      privateKey: env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, "\n"),
      clientEmail: env.FIREBASE_CLIENT_EMAIL,
    };

    firebaseApp = admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: env.FIREBASE_PROJECT_ID,
    });

    console.log("✅ Firebase Admin SDK initialized successfully");
    return firebaseApp;
  } catch (error) {
    console.error("❌ Failed to initialize Firebase Admin SDK:", error);
    console.log("   Continuing without Firebase - some features may not work");
    return null;
  }
}

export function getFirestore(): admin.firestore.Firestore | null {
  if (!firebaseApp) {
    const app = initializeFirebase();
    if (!app) {
      console.warn("⚠️ Firestore not available - Firebase not initialized");
      return null;
    }
  }
  return admin.firestore();
}

export function getAuth(): admin.auth.Auth | null {
  if (!firebaseApp) {
    const app = initializeFirebase();
    if (!app) {
      console.warn("⚠️ Auth not available - Firebase not initialized");
      return null;
    }
  }
  return admin.auth();
}

export { admin };
