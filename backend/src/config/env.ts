import { config } from "dotenv";
import { z } from "zod";

// Load environment variables
config();

const envSchema = z.object({
  // Server Configuration
  PORT: z.string().default("3001").transform(Number),
  NODE_ENV: z
    .enum(["development", "production", "test"])
    .default("development"),
  CORS_ORIGIN: z.string().default("http://localhost:9002"),

  // Google AI Configuration
  GOOGLE_GENAI_API_KEY: z.string().min(1, "Google AI API key is required"),

  // Firebase Configuration
  FIREBASE_PROJECT_ID: z.string().min(1, "Firebase project ID is required"),
  FIREBASE_PRIVATE_KEY: z.string().min(1, "Firebase private key is required"),
  FIREBASE_CLIENT_EMAIL: z.string().min(1, "Firebase client email is required"),

  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: z.string().default("900000").transform(Number),
  RATE_LIMIT_MAX_REQUESTS: z.string().default("100").transform(Number),

  // Logging
  LOG_LEVEL: z.enum(["error", "warn", "info", "debug"]).default("info"),
});

export type EnvConfig = z.infer<typeof envSchema>;

let env: EnvConfig;

try {
  env = envSchema.parse(process.env);
} catch (error) {
  console.error("❌ Invalid environment configuration:", error);
  process.exit(1);
}

export { env };
