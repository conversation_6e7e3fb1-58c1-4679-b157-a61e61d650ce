import { Section, UserType, QuestionType } from '@/types/questions';

export const professionalSections: Section[] = [
  {
    id: 'interest_passion_analysis',
    title: 'Interest & Passion Analysis',
    description: 'Understand what drives your professional engagement and satisfaction.',
    userType: UserType.PROFESSIONAL,
    questions: [
      {
        id: 'exciting_activities',
        type: QuestionType.CHECKBOX,
        text: 'Which of these activities excite you the most? (Choose 3)',
        options: [
          'Strategic planning',
          'Problem solving',
          'Creative design',
          'Data analysis',
          'Team leadership',
          'Client interaction',
          'Research & development',
          'Training & mentoring',
          'Process improvement',
          'Innovation & ideation'
        ],
        maxSelections: 3,
        required: true
      },
      {
        id: 'problem_vs_creation',
        type: QuestionType.MCQ,
        text: 'Do you feel more energized solving problems or creating new ideas?',
        options: ['Solving existing problems', 'Creating new ideas', 'Both equally'],
        required: true
      },
      {
        id: 'unpaid_work_domain',
        type: QuestionType.SHORT_ANSWER,
        text: 'Which domain would you love to work in even without pay for 1 month?',
        required: true
      },
      {
        id: 'daily_task_anticipation',
        type: QuestionType.SHORT_ANSWER,
        text: 'What\'s one professional task you look forward to daily?',
        required: true
      },
      {
        id: 'work_preference_type',
        type: QuestionType.MCQ,
        text: 'Do you prefer working with numbers, people, data, or products?',
        options: ['Numbers & analytics', 'People & relationships', 'Data & insights', 'Products & services', 'Mix of all'],
        required: true
      },
      {
        id: 'energy_draining_tasks',
        type: QuestionType.SHORT_ANSWER,
        text: 'Which type of tasks drain your energy the most?',
        required: true
      },
      {
        id: 'leadership_vs_technical',
        type: QuestionType.MCQ,
        text: 'Would you rather lead a team or master a technical skill?',
        options: ['Lead a team', 'Master technical skills', 'Both are equally important'],
        required: true
      },
      {
        id: 'industry_trends_following',
        type: QuestionType.SHORT_ANSWER,
        text: 'Do you follow industry trends or innovations regularly? What sources do you use?',
        required: true
      },
      {
        id: 'emotional_connection_industries',
        type: QuestionType.CHECKBOX,
        text: 'Which industries do you feel emotionally connected to?',
        options: [
          'Healthcare',
          'Education',
          'Technology',
          'Finance',
          'Environment',
          'Social impact',
          'Entertainment',
          'Manufacturing',
          'Consulting',
          'Retail'
        ],
        required: true
      },
      {
        id: 'admired_roles',
        type: QuestionType.MCQ,
        text: 'Which type of roles do you admire in others?',
        options: ['Manager', 'Analyst', 'Designer', 'Researcher', 'Founder', 'Educator', 'Consultant'],
        required: true
      }
    ]
  },
  {
    id: 'personality_work_style',
    title: 'Personality & Work Style',
    description: 'Assess your decision-making, teamwork, and behavioral fit.',
    userType: UserType.PROFESSIONAL,
    questions: [
      {
        id: 'pressure_handling',
        type: QuestionType.MCQ,
        text: 'How do you handle pressure at work?',
        options: ['Thrive under pressure', 'Manage pressure well', 'Struggle with pressure', 'Prefer to avoid pressure'],
        required: true
      },
      {
        id: 'team_role',
        type: QuestionType.MCQ,
        text: 'What role do you usually play in teams?',
        options: ['Leader', 'Strategist', 'Implementer', 'Mediator', 'Creative contributor'],
        required: true
      },
      {
        id: 'planning_vs_flexible',
        type: QuestionType.MCQ,
        text: 'Are you more of a planner or a go-with-the-flow professional?',
        options: ['Detailed planner', 'Flexible & adaptive', 'Balanced mix of both'],
        required: true
      },
      {
        id: 'decision_making_style',
        type: QuestionType.MCQ,
        text: 'How do you make critical decisions?',
        options: ['Based on data & analysis', 'Trust my instincts', 'Seek peer input', 'Combination of all'],
        required: true
      },
      {
        id: 'autonomy_vs_collaboration',
        type: QuestionType.MCQ,
        text: 'Do you prefer high autonomy or constant collaboration in your work?',
        options: ['High autonomy', 'Constant collaboration', 'Balanced mix', 'Depends on the project'],
        required: true
      },
      {
        id: 'feedback_comfort',
        type: QuestionType.SCALE,
        text: 'Are you comfortable giving and receiving feedback openly?',
        scaleMin: 1,
        scaleMax: 5,
        scaleLabels: ['Very uncomfortable', 'Uncomfortable', 'Neutral', 'Comfortable', 'Very comfortable'],
        required: true
      },
      {
        id: 'efficiency_vs_perfection',
        type: QuestionType.MCQ,
        text: 'Do you prioritize efficiency or perfection in your work style?',
        options: ['Efficiency first', 'Perfection first', 'Balanced approach', 'Depends on the task'],
        required: true
      },
      {
        id: 'initiative_taking',
        type: QuestionType.SHORT_ANSWER,
        text: 'Do you take the initiative in new projects at work? Give an example.',
        required: true
      },
      {
        id: 'growth_reflection',
        type: QuestionType.SCALE,
        text: 'How frequently do you reflect on your growth and set career goals?',
        scaleMin: 1,
        scaleMax: 5,
        scaleLabels: ['Never', 'Rarely', 'Sometimes', 'Often', 'Very frequently'],
        required: true
      },
      {
        id: 'ambiguity_comfort',
        type: QuestionType.SCALE,
        text: 'How comfortable are you with ambiguity and changing plans?',
        scaleMin: 1,
        scaleMax: 5,
        scaleLabels: ['Very uncomfortable', 'Uncomfortable', 'Neutral', 'Comfortable', 'Very comfortable'],
        required: true
      }
    ]
  }
];
