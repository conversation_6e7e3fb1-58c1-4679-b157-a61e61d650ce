import { Section, UserType, QuestionType } from "@/types/questions";

export const studentSections: Section[] = [
  {
    id: "interest_subject_preferences",
    title: "Interest & Subject Preferences",
    description:
      "Help us understand what you genuinely enjoy learning and doing.",
    userType: UserType.STUDENT,
    questions: [
      {
        id: "enjoy_subjects",
        type: QuestionType.CHECKBOX,
        text: "Which subjects do you enjoy the most? (Select up to 3)",
        options: [
          "Mathematics",
          "Physics",
          "Chemistry",
          "Biology",
          "History",
          "Geography",
          "Literature",
          "Art",
          "Music",
          "Computer Science",
        ],
        maxSelections: 3,
        required: true,
      },
      {
        id: "favorite_project",
        type: QuestionType.SHORT_ANSWER,
        text: "Which school project did you enjoy working on the most, and why?",
        required: true,
      },
      {
        id: "work_preference",
        type: QuestionType.MCQ,
        text: "Do you prefer working with facts, stories, or numbers?",
        options: ["Facts", "Stories", "Numbers"],
        required: true,
      },
      {
        id: "extra_time_subject",
        type: QuestionType.SHORT_ANSWER,
        text: "Which subject do you spend extra time on without being told?",
        required: true,
      },
      {
        id: "creative_vs_logical",
        type: QuestionType.MCQ,
        text: "Do you enjoy creative tasks (drawing, music, etc.) or logical ones (math problems)?",
        options: ["Creative tasks", "Logical tasks", "Both equally"],
        required: true,
      },
      {
        id: "extracurricular_activities",
        type: QuestionType.CHECKBOX,
        text: "Which extracurricular activities do you actively participate in?",
        options: [
          "Sports",
          "Music",
          "Art",
          "Drama/Theater",
          "Debate",
          "Science Club",
          "Math Club",
          "Student Government",
          "Volunteer Work",
          "Coding/Programming",
        ],
        required: false,
      },
      {
        id: "building_experimenting",
        type: QuestionType.YES_NO,
        text: "Do you like building, fixing, or experimenting with things?",
        required: true,
      },
      {
        id: "fun_topics",
        type: QuestionType.SHORT_ANSWER,
        text: "Which topics or YouTube videos do you often explore for fun?",
        required: true,
      },
      {
        id: "teaching_others",
        type: QuestionType.YES_NO,
        text: "Have you ever taught someone a topic you love?",
        required: true,
      },
      {
        id: "class_activity_preference",
        type: QuestionType.MCQ,
        text: "What kind of class activity excites you more?",
        options: [
          "Group discussions",
          "Experiments",
          "Presentations",
          "Reading",
        ],
        required: true,
      },
      {
        id: "online_research",
        type: QuestionType.YES_NO,
        text: "Do you enjoy researching and learning about new things online?",
        required: true,
      },
      {
        id: "skip_subject",
        type: QuestionType.SHORT_ANSWER,
        text: "If you could skip one subject, which one would it be and why?",
        required: true,
      },
      {
        id: "competitions",
        type: QuestionType.CHECKBOX,
        text: "Which competitions do you enjoy?",
        options: [
          "Quiz competitions",
          "Sports",
          "Drawing/Art",
          "Debates",
          "Coding",
          "Science fairs",
          "Math olympiad",
          "Writing contests",
        ],
        required: false,
      },
      {
        id: "career_association",
        type: QuestionType.SHORT_ANSWER,
        text: "What career do you associate your favorite subject with?",
        required: true,
      },
      {
        id: "teacher_dependency",
        type: QuestionType.SHORT_ANSWER,
        text: "Do you think your interest in a subject depends on the teacher? Why?",
        required: true,
      },
    ],
  },
  {
    id: "personality_learning_style",
    title: "Personality & Learning Style",
    description: "Find your best learning and working style.",
    userType: UserType.STUDENT,
    questions: [
      {
        id: "study_method",
        type: QuestionType.CHECKBOX,
        text: "When studying, which methods do you prefer?",
        options: [
          "Reading textbooks",
          "Watching videos",
          "Taking notes",
          "Making diagrams",
          "Group study",
          "Flashcards",
          "Practice problems",
          "Teaching others",
        ],
        required: true,
      },
      {
        id: "analytical_vs_imaginative",
        type: QuestionType.MCQ,
        text: "Are you more analytical or imaginative in your approach?",
        options: [
          "More analytical",
          "More imaginative",
          "Balanced mix of both",
        ],
        required: true,
      },
      {
        id: "puzzles_games",
        type: QuestionType.YES_NO,
        text: "Do you enjoy solving puzzles, riddles, or brain games?",
        required: true,
      },
      {
        id: "study_environment",
        type: QuestionType.MCQ,
        text: "Do you need complete silence to focus, or can you study with background noise?",
        options: [
          "Complete silence",
          "Light background music",
          "Normal household noise is fine",
          "I can focus anywhere",
        ],
        required: true,
      },
      {
        id: "motivation_strategy",
        type: QuestionType.SHORT_ANSWER,
        text: "How do you stay motivated when a topic is tough?",
        required: true,
      },
      {
        id: "goal_setting",
        type: QuestionType.YES_NO,
        text: "Do you set daily/weekly study goals for yourself?",
        required: true,
      },
      {
        id: "learning_style",
        type: QuestionType.MCQ,
        text: "Do you learn better by doing things or by watching others?",
        options: [
          "By doing things myself",
          "By watching others first",
          "By reading instructions",
          "By listening to explanations",
        ],
        required: true,
      },
      {
        id: "reflection_frequency",
        type: QuestionType.SCALE,
        text: "How often do you reflect on your mistakes and improve?",
        scaleMin: 1,
        scaleMax: 5,
        scaleLabels: ["Never", "Rarely", "Sometimes", "Often", "Always"],
        required: true,
      },
      {
        id: "group_role",
        type: QuestionType.MCQ,
        text: "Are you someone who leads or follows in group projects?",
        options: [
          "Usually lead",
          "Sometimes lead",
          "Usually follow",
          "Prefer to work alone",
        ],
        required: true,
      },
      {
        id: "learner_type",
        type: QuestionType.MCQ,
        text: "What kind of learner are you?",
        options: [
          "Visual (learn by seeing)",
          "Auditory (learn by hearing)",
          "Kinesthetic (learn by doing)",
          "Reading-Writing (learn by reading/writing)",
        ],
        required: true,
      },
    ],
  },
  {
    id: "skills_strengths",
    title: "Skills & Strengths",
    description: "Gauge your practical, academic, and interpersonal strengths.",
    userType: UserType.STUDENT,
    questions: [
      {
        id: "skill_ratings",
        type: QuestionType.SCALE,
        text: "Rate yourself in Communication (1-5)",
        scaleMin: 1,
        scaleMax: 5,
        scaleLabels: ["Poor", "Below Average", "Average", "Good", "Excellent"],
        required: true,
      },
      {
        id: "problem_solving_rating",
        type: QuestionType.SCALE,
        text: "Rate yourself in Problem Solving (1-5)",
        scaleMin: 1,
        scaleMax: 5,
        scaleLabels: ["Poor", "Below Average", "Average", "Good", "Excellent"],
        required: true,
      },
      {
        id: "leadership_rating",
        type: QuestionType.SCALE,
        text: "Rate yourself in Leadership (1-5)",
        scaleMin: 1,
        scaleMax: 5,
        scaleLabels: ["Poor", "Below Average", "Average", "Good", "Excellent"],
        required: true,
      },
      {
        id: "teamwork_rating",
        type: QuestionType.SCALE,
        text: "Rate yourself in Teamwork (1-5)",
        scaleMin: 1,
        scaleMax: 5,
        scaleLabels: ["Poor", "Below Average", "Average", "Good", "Excellent"],
        required: true,
      },
      {
        id: "technology_rating",
        type: QuestionType.SCALE,
        text: "Rate yourself in Technology Use (1-5)",
        scaleMin: 1,
        scaleMax: 5,
        scaleLabels: ["Poor", "Below Average", "Average", "Good", "Excellent"],
        required: true,
      },
      {
        id: "creativity_rating",
        type: QuestionType.SCALE,
        text: "Rate yourself in Creativity (1-5)",
        scaleMin: 1,
        scaleMax: 5,
        scaleLabels: ["Poor", "Below Average", "Average", "Good", "Excellent"],
        required: true,
      },
      {
        id: "confident_activities",
        type: QuestionType.SHORT_ANSWER,
        text: "What activities make you feel most confident?",
        required: true,
      },
      {
        id: "created_something",
        type: QuestionType.SHORT_ANSWER,
        text: "Have you ever created something from scratch? (Art, code, project, etc.) Describe it.",
        required: true,
      },
      {
        id: "recent_problem_solved",
        type: QuestionType.SHORT_ANSWER,
        text: "What's one problem you solved recently?",
        required: true,
      },
      {
        id: "public_speaking_comfort",
        type: QuestionType.SCALE,
        text: "How comfortable are you with public speaking?",
        scaleMin: 1,
        scaleMax: 5,
        scaleLabels: [
          "Very uncomfortable",
          "Uncomfortable",
          "Neutral",
          "Comfortable",
          "Very comfortable",
        ],
        required: true,
      },
    ],
  },
];
