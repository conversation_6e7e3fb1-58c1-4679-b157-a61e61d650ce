import { Response, NextFunction } from "express";
import { getAuth } from "@/config/firebase";
import { AuthenticatedRequest } from "@/types/express";

export async function authenticateToken(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const auth = getAuth();

    if (!auth) {
      console.warn("⚠️ Authentication skipped - Firebase not initialized");
      next();
      return;
    }

    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      res.status(401).json({
        success: false,
        error: "No valid authorization token provided",
      });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify the Firebase ID token
    const decodedToken = await auth.verifyIdToken(token);

    // Add user information to request
    req.user = {
      uid: decodedToken.uid,
      ...(decodedToken.email && { email: decodedToken.email }),
      ...(decodedToken.email_verified !== undefined && {
        emailVerified: decodedToken.email_verified,
      }),
    };

    next();
  } catch (error) {
    console.error("Authentication error:", error);
    res.status(401).json({
      success: false,
      error: "Invalid or expired token",
    });
  }
}

// Optional authentication middleware - doesn't fail if no token provided
export async function optionalAuth(
  req: AuthenticatedRequest,
  _res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const auth = getAuth();

    if (!auth) {
      console.warn(
        "⚠️ Optional authentication skipped - Firebase not initialized"
      );
      next();
      return;
    }

    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith("Bearer ")) {
      const token = authHeader.substring(7);
      const decodedToken = await auth.verifyIdToken(token);

      req.user = {
        uid: decodedToken.uid,
        ...(decodedToken.email && { email: decodedToken.email }),
        ...(decodedToken.email_verified !== undefined && {
          emailVerified: decodedToken.email_verified,
        }),
      };
    }

    next();
  } catch (error) {
    // For optional auth, we don't fail on invalid tokens
    console.warn("Optional auth failed:", error);
    next();
  }
}
